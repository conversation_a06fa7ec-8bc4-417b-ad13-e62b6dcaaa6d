import argparse
import os
import re
from collections import Counter, defaultdict
from typing import List, Dict, Set

import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from tree_sitter_languages import get_language
from tree_sitter import Language, Node, Parser

from build_graph_c_common import (
    read_file_safely, is_c_file, is_header_file, is_source_file,
    extract_includes_from_content, resolve_include_path, 
    extract_function_calls_from_content, find_common_include_directories,
    extract_macros_from_content, is_skip_directory, normalize_path,
    extract_struct_usage_from_content
)

VERSION = 'v1.0'
NODE_TYPE_DIRECTORY = 'directory'
NODE_TYPE_FILE = 'file'
NODE_TYPE_FUNCTION = 'function'
NODE_TYPE_STRUCT = 'struct'
NODE_TYPE_UNION = 'union'
NODE_TYPE_ENUM = 'enum'
NODE_TYPE_TYPEDEF = 'typedef'
NODE_TYPE_VARIABLE = 'variable'
NODE_TYPE_MACRO = 'macro'
EDGE_TYPE_CONTAINS = 'contains'
EDGE_TYPE_INCLUDES = 'includes'
EDGE_TYPE_CALLS = 'calls'
EDGE_TYPE_USES = 'uses'
EDGE_TYPE_DEFINES = 'defines'

VALID_NODE_TYPES = [NODE_TYPE_DIRECTORY, NODE_TYPE_FILE, NODE_TYPE_FUNCTION, 
                   NODE_TYPE_STRUCT, NODE_TYPE_UNION, NODE_TYPE_ENUM, 
                   NODE_TYPE_TYPEDEF, NODE_TYPE_VARIABLE, NODE_TYPE_MACRO]
VALID_EDGE_TYPES = [EDGE_TYPE_CONTAINS, EDGE_TYPE_INCLUDES, EDGE_TYPE_CALLS, 
                   EDGE_TYPE_USES, EDGE_TYPE_DEFINES]


class CCodeAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.nodes = []
        self.includes = []
        self.function_calls = []
        self.struct_usage = []
        self.macros = []
        self.parser = Parser()
        self.parser.set_language(get_language('c'))
        
    def analyze(self, content):
        """Analyze C file content using tree-sitter"""
        tree = self.parser.parse(content.encode('utf-8'))
        self._traverse_node(tree.root_node, content)
        
        # Also extract additional information using regex
        self.includes = extract_includes_from_content(content)
        self.function_calls = extract_function_calls_from_content(content)
        self.struct_usage = extract_struct_usage_from_content(content)
        self.macros = extract_macros_from_content(content)
        
        return self.nodes, self.includes, self.function_calls, self.struct_usage, self.macros
    
    def _traverse_node(self, node: Node, content: str, parent_name: str = ""):
        """Traverse tree-sitter AST nodes"""
        if node.type == 'function_definition':
            self._handle_function(node, content, parent_name)
        elif node.type == 'struct_specifier':
            self._handle_struct(node, content, parent_name)
        elif node.type == 'union_specifier':
            self._handle_union(node, content, parent_name)
        elif node.type == 'enum_specifier':
            self._handle_enum(node, content, parent_name)
        elif node.type == 'typedef_declaration':
            self._handle_typedef(node, content, parent_name)
        elif node.type == 'declaration':
            self._handle_variable(node, content, parent_name)
        elif node.type == 'preproc_def' or node.type == 'preproc_function_def':
            self._handle_macro(node, content, parent_name)
        
        # Recursively traverse child nodes
        for child in node.children:
            self._traverse_node(child, content, parent_name)
    
    def _get_identifier(self, node: Node) -> str:
        """Extract identifier from a node"""
        for child in node.children:
            if child.type == 'identifier':
                return child.text.decode('utf-8')
            elif child.type == 'type_identifier':
                return child.text.decode('utf-8')
        return ""
    
    def _get_source_segment(self, node: Node, content: str) -> str:
        """Get source code segment for a node"""
        start_byte = node.start_byte
        end_byte = node.end_byte
        return content.encode('utf-8')[start_byte:end_byte].decode('utf-8')
    
    def _handle_function(self, node: Node, content: str, parent_name: str):
        """Handle function definition"""
        func_name = ""
        
        # Find function declarator
        for child in node.children:
            if child.type == 'function_declarator':
                func_name = self._get_identifier(child)
                break
        
        if func_name:
            full_name = f"{parent_name}.{func_name}" if parent_name else func_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_FUNCTION,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'parameters': self._get_function_parameters(node)
            })
    
    def _handle_struct(self, node: Node, content: str, parent_name: str):
        """Handle struct definition"""
        struct_name = self._get_identifier(node)
        if struct_name:
            full_name = f"{parent_name}.{struct_name}" if parent_name else struct_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_STRUCT,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'fields': self._get_struct_fields(node)
            })
    
    def _handle_union(self, node: Node, content: str, parent_name: str):
        """Handle union definition"""
        union_name = self._get_identifier(node)
        if union_name:
            full_name = f"{parent_name}.{union_name}" if parent_name else union_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_UNION,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'fields': self._get_struct_fields(node)  # Same as struct fields
            })
    
    def _handle_enum(self, node: Node, content: str, parent_name: str):
        """Handle enum definition"""
        enum_name = self._get_identifier(node)
        if enum_name:
            full_name = f"{parent_name}.{enum_name}" if parent_name else enum_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_ENUM,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'values': self._get_enum_values(node)
            })
    
    def _handle_typedef(self, node: Node, content: str, parent_name: str):
        """Handle typedef declaration"""
        typedef_name = ""
        
        # Find the typedef name (usually the last identifier)
        identifiers = []
        for child in node.children:
            if child.type == 'type_identifier' or child.type == 'identifier':
                identifiers.append(child.text.decode('utf-8'))
        
        if identifiers:
            typedef_name = identifiers[-1]  # Last identifier is usually the typedef name
        
        if typedef_name:
            full_name = f"{parent_name}.{typedef_name}" if parent_name else typedef_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_TYPEDEF,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1
            })
    
    def _handle_variable(self, node: Node, content: str, parent_name: str):
        """Handle variable declaration"""
        var_names = self._extract_variable_names(node)
        for var_name in var_names:
            full_name = f"{parent_name}.{var_name}" if parent_name else var_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_VARIABLE,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1
            })
    
    def _handle_macro(self, node: Node, content: str, parent_name: str):
        """Handle macro definition"""
        macro_name = self._get_identifier(node)
        if macro_name:
            full_name = f"{parent_name}.{macro_name}" if parent_name else macro_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_MACRO,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'is_function_like': node.type == 'preproc_function_def'
            })
    
    def _get_function_parameters(self, node: Node) -> List[str]:
        """Extract function parameters"""
        parameters = []
        for child in node.children:
            if child.type == 'function_declarator':
                for grandchild in child.children:
                    if grandchild.type == 'parameter_list':
                        for param in grandchild.children:
                            if param.type == 'parameter_declaration':
                                param_name = self._get_identifier(param)
                                if param_name:
                                    parameters.append(param_name)
        return parameters
    
    def _get_struct_fields(self, node: Node) -> List[str]:
        """Extract struct/union fields"""
        fields = []
        for child in node.children:
            if child.type == 'field_declaration_list':
                for field_decl in child.children:
                    if field_decl.type == 'field_declaration':
                        field_name = self._get_identifier(field_decl)
                        if field_name:
                            fields.append(field_name)
        return fields
    
    def _get_enum_values(self, node: Node) -> List[str]:
        """Extract enum values"""
        values = []
        for child in node.children:
            if child.type == 'enumerator_list':
                for enum_val in child.children:
                    if enum_val.type == 'enumerator':
                        val_name = self._get_identifier(enum_val)
                        if val_name:
                            values.append(val_name)
        return values
    
    def _extract_variable_names(self, node: Node) -> List[str]:
        """Extract variable names from declaration"""
        names = []
        for child in node.children:
            if child.type == 'init_declarator':
                var_name = self._get_identifier(child)
                if var_name:
                    names.append(var_name)
        return names


def analyze_c_file(filepath):
    """Analyze a C file and extract functions, structs, includes"""
    try:
        content = read_file_safely(filepath)
    except:
        raise UnicodeDecodeError

    try:
        analyzer = CCodeAnalyzer(filepath)
        nodes, includes, function_calls, struct_usage, macros = analyzer.analyze(content)
        return nodes, includes, function_calls, struct_usage, macros
    except Exception as e:
        raise SyntaxError(f"Failed to parse {filepath}: {e}")


def add_c_includes(root_node, includes, graph, repo_path, include_dirs):
    """Add include edges to the graph"""
    current_file_dir = os.path.dirname(os.path.join(repo_path, root_node))

    for inc in includes:
        include_path = inc['path']

        # Skip system includes for now
        if inc['is_system']:
            continue

        # Try to resolve the included file
        resolved_path = resolve_include_path(include_path, current_file_dir, repo_path, include_dirs)

        if resolved_path and graph.has_node(resolved_path):
            graph.add_edge(root_node, resolved_path, type=EDGE_TYPE_INCLUDES)


def build_c_graph(repo_path, fuzzy_search=True):
    """
    Build dependency graph for C files in the repository
    """
    graph = nx.MultiDiGraph()
    file_nodes = {}

    # Find common include directories
    include_dirs = find_common_include_directories(repo_path)

    # Add root directory node
    graph.add_node('/', type=NODE_TYPE_DIRECTORY)
    dir_stack: List[str] = []
    dir_include_stack: List[bool] = []

    # First pass: Add nodes (directories, files, functions, structs)
    for root, _, files in os.walk(repo_path):
        # Add directory nodes and edges
        dirname = os.path.relpath(root, repo_path)
        if dirname == '.':
            dirname = '/'
        elif is_skip_directory(dirname):
            continue
        else:
            graph.add_node(dirname, type=NODE_TYPE_DIRECTORY)
            parent_dirname = os.path.dirname(dirname)
            if parent_dirname == '':
                parent_dirname = '/'
            graph.add_edge(parent_dirname, dirname, type=EDGE_TYPE_CONTAINS)

        # Remove directories that don't contain C files
        while len(dir_stack) > 0 and not dirname.startswith(dir_stack[-1]):
            if not dir_include_stack[-1]:
                graph.remove_node(dir_stack[-1])
            dir_stack.pop()
            dir_include_stack.pop()
        if dirname != '/':
            dir_stack.append(dirname)
            dir_include_stack.append(False)

        dir_has_c = False
        for file in files:
            if is_c_file(file):
                dir_has_c = True

                # Add file nodes
                try:
                    file_path = os.path.join(root, file)
                    filename = os.path.relpath(file_path, repo_path)

                    if os.path.islink(file_path):
                        continue

                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    graph.add_node(filename, type=NODE_TYPE_FILE, code=file_content)

                    # Analyze C file
                    nodes, includes, function_calls, struct_usage, macros = analyze_c_file(file_path)

                except (UnicodeDecodeError, SyntaxError) as e:
                    print(f"Skipping file {file_path}: {e}")
                    continue

                # Add function/struct/macro nodes
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    node_attrs = {
                        'type': node['type'],
                        'code': node['code'],
                        'start_line': node['start_line'],
                        'end_line': node['end_line']
                    }

                    # Add type-specific attributes
                    if 'parameters' in node:
                        node_attrs['parameters'] = node['parameters']
                    if 'fields' in node:
                        node_attrs['fields'] = node['fields']
                    if 'values' in node:
                        node_attrs['values'] = node['values']
                    if 'is_function_like' in node:
                        node_attrs['is_function_like'] = node['is_function_like']

                    graph.add_node(full_name, **node_attrs)

                # Add containment edges
                graph.add_edge(dirname, filename, type=EDGE_TYPE_CONTAINS)
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    name_parts = node['name'].split('.')
                    if len(name_parts) == 1:
                        # Top-level declaration
                        graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)
                    else:
                        # Nested declaration
                        parent_name = '.'.join(name_parts[:-1])
                        full_parent_name = f'{filename}:{parent_name}'
                        if graph.has_node(full_parent_name):
                            graph.add_edge(full_parent_name, full_name, type=EDGE_TYPE_CONTAINS)
                        else:
                            # Fallback to file containment
                            graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)

                # Store analysis results for later processing
                file_nodes[filename] = {
                    'path': file_path,
                    'includes': includes,
                    'function_calls': function_calls,
                    'struct_usage': struct_usage,
                    'macros': macros
                }

        # Keep all parent directories that contain C files
        if dir_has_c:
            for i in range(len(dir_include_stack)):
                dir_include_stack[i] = True

    # Clean up remaining directories
    while len(dir_stack) > 0:
        if not dir_include_stack[-1]:
            graph.remove_node(dir_stack[-1])
        dir_stack.pop()
        dir_include_stack.pop()

    # Second pass: Add include, call, and usage edges
    for filename, file_info in file_nodes.items():
        if isinstance(file_info, dict):
            includes = file_info['includes']
            function_calls = file_info['function_calls']
            struct_usage = file_info['struct_usage']

            # Add include edges
            add_c_includes(filename, includes, graph, repo_path, include_dirs)

            # Add function call edges
            for func_name in function_calls:
                target_nodes = find_function_nodes(graph, func_name, fuzzy_search)
                for target_node in target_nodes:
                    # Find the calling function (if any)
                    calling_functions = find_functions_in_file(graph, filename)
                    for calling_func in calling_functions:
                        graph.add_edge(calling_func, target_node, type=EDGE_TYPE_CALLS)

            # Add struct usage edges
            for struct_name in struct_usage:
                target_nodes = find_struct_nodes(graph, struct_name, fuzzy_search)
                for target_node in target_nodes:
                    graph.add_edge(filename, target_node, type=EDGE_TYPE_USES)

    return graph, file_nodes


def find_function_nodes(graph, func_name, fuzzy_search=True):
    """Find function nodes by name"""
    matching_nodes = []

    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') == NODE_TYPE_FUNCTION:
            node_name = node.split(':')[-1] if ':' in node else node

            if fuzzy_search:
                # Match by simple function name
                if node_name.split('.')[-1] == func_name:
                    matching_nodes.append(node)
            else:
                # Exact match
                if node_name == func_name:
                    matching_nodes.append(node)

    return matching_nodes


def find_struct_nodes(graph, struct_name, fuzzy_search=True):
    """Find struct/union/enum nodes by name"""
    matching_nodes = []

    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') in [NODE_TYPE_STRUCT, NODE_TYPE_UNION, NODE_TYPE_ENUM]:
            node_name = node.split(':')[-1] if ':' in node else node

            if fuzzy_search:
                # Match by simple struct name
                if node_name.split('.')[-1] == struct_name:
                    matching_nodes.append(node)
            else:
                # Exact match
                if node_name == struct_name:
                    matching_nodes.append(node)

    return matching_nodes


def find_functions_in_file(graph, filename):
    """Find all function nodes in a specific file"""
    function_nodes = []

    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') == NODE_TYPE_FUNCTION and node.startswith(f"{filename}:"):
            function_nodes.append(node)

    return function_nodes


def visualize_c_graph(G):
    """Visualize the C dependency graph"""
    node_types = set(nx.get_node_attributes(G, 'type').values())
    node_shapes = {
        NODE_TYPE_FUNCTION: 's',
        NODE_TYPE_STRUCT: 'o',
        NODE_TYPE_UNION: '^',
        NODE_TYPE_ENUM: 'v',
        NODE_TYPE_TYPEDEF: 'p',
        NODE_TYPE_VARIABLE: '*',
        NODE_TYPE_MACRO: '+',
        NODE_TYPE_FILE: 'D',
        NODE_TYPE_DIRECTORY: 'h'
    }
    node_colors = {
        NODE_TYPE_FUNCTION: 'lightblue',
        NODE_TYPE_STRUCT: 'lightgreen',
        NODE_TYPE_UNION: 'lightcoral',
        NODE_TYPE_ENUM: 'lightyellow',
        NODE_TYPE_TYPEDEF: 'lightpink',
        NODE_TYPE_VARIABLE: 'lightcyan',
        NODE_TYPE_MACRO: 'lavender',
        NODE_TYPE_FILE: 'lightgrey',
        NODE_TYPE_DIRECTORY: 'orange'
    }

    edge_types = set(nx.get_edge_attributes(G, 'type').values())
    edge_colors = {
        EDGE_TYPE_INCLUDES: 'forestgreen',
        EDGE_TYPE_CONTAINS: 'skyblue',
        EDGE_TYPE_CALLS: 'magenta',
        EDGE_TYPE_USES: 'purple',
        EDGE_TYPE_DEFINES: 'brown'
    }
    edge_styles = {
        EDGE_TYPE_INCLUDES: 'solid',
        EDGE_TYPE_CONTAINS: 'dashed',
        EDGE_TYPE_CALLS: 'dotted',
        EDGE_TYPE_USES: (0, (3, 1, 1, 1)),
        EDGE_TYPE_DEFINES: 'dashdot'
    }

    pos = nx.spring_layout(G, k=2, iterations=50)

    plt.figure(figsize=(20, 20))
    plt.margins(0.15)

    # Draw nodes with different shapes and colors based on their type
    for ntype in node_types:
        nodelist = [n for n, d in G.nodes(data=True) if d['type'] == ntype]
        if nodelist:  # Only draw if there are nodes of this type
            nx.draw_networkx_nodes(
                G,
                pos,
                nodelist=nodelist,
                node_shape=node_shapes.get(ntype, 'o'),
                node_color=node_colors.get(ntype, 'lightgray'),
                node_size=700,
                label=ntype,
            )

    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=8, font_family='sans-serif')

    # Group edges between the same pair of nodes
    edge_groups = {}
    for u, v, key, data in G.edges(keys=True, data=True):
        if (u, v) not in edge_groups:
            edge_groups[(u, v)] = []
        edge_groups[(u, v)].append((key, data))

    # Draw edges with adjusted 'rad' values
    for (u, v), edges in edge_groups.items():
        num_edges = len(edges)
        for i, (key, data) in enumerate(edges):
            edge_type = data['type']
            # Adjust 'rad' to spread the edges
            rad = 0.1 * (i - (num_edges - 1) / 2)
            nx.draw_networkx_edges(
                G,
                pos,
                edgelist=[(u, v)],
                edge_color=edge_colors.get(edge_type, 'black'),
                style=edge_styles.get(edge_type, 'solid'),
                connectionstyle=f'arc3,rad={rad}',
                arrows=True,
                arrowstyle='-|>',
                arrowsize=15,
                min_source_margin=15,
                min_target_margin=15,
                width=1.5
            )

    # Create legends for edge types and node types
    edge_legend_elements = [
        Line2D([0], [0], color=edge_colors.get(etype, 'black'), lw=2,
               linestyle=edge_styles.get(etype, 'solid'), label=etype)
        for etype in edge_types
    ]
    node_legend_elements = [
        Line2D([0], [0], marker=node_shapes.get(ntype, 'o'), color='w', label=ntype,
               markerfacecolor=node_colors.get(ntype, 'lightgray'), markersize=15)
        for ntype in node_types
    ]

    # Combine legends
    plt.legend(handles=edge_legend_elements + node_legend_elements, loc='upper left')
    plt.axis('off')
    plt.savefig('plots/c_dependency_graph.png', dpi=300, bbox_inches='tight')
    plt.show()


def traverse_c_directory_structure(graph, root='/'):
    """Traverse and print the directory structure of the C project"""
    def traverse(node, prefix, is_last):
        if node == root:
            print(f"{node}")
            new_prefix = ''
        else:
            connector = '└── ' if is_last else '├── '
            print(f"{prefix}{connector}{node}")
            new_prefix = prefix + ('    ' if is_last else '│   ')

        # Stop if the current node is a file (leaf node)
        if graph.nodes[node].get('type') == NODE_TYPE_FILE:
            return

        # Traverse neighbors with edge type 'contains'
        neighbors = list(graph.neighbors(node))
        for i, neighbor in enumerate(neighbors):
            for key in graph[node][neighbor]:
                if graph[node][neighbor][key].get('type') == EDGE_TYPE_CONTAINS:
                    is_last_child = (i == len(neighbors) - 1)
                    traverse(neighbor, new_prefix, is_last_child)

    traverse(root, '', False)


def main():
    """Main function to build and analyze C dependency graph"""
    parser = argparse.ArgumentParser(description='Build dependency graph for C projects')
    parser.add_argument('--repo_path', type=str, required=True,
                        help='Path to the C repository root')
    parser.add_argument('--visualize', action='store_true',
                        help='Generate visualization of the dependency graph')
    parser.add_argument('--fuzzy_search', action='store_true', default=True,
                        help='Enable fuzzy search for function and struct matching')
    parser.add_argument('--output_dir', type=str, default='plots',
                        help='Directory to save visualization plots')

    args = parser.parse_args()

    # Ensure output directory exists
    if args.visualize:
        os.makedirs(args.output_dir, exist_ok=True)

    print(f"Building C dependency graph for: {args.repo_path}")

    try:
        # Build the dependency graph
        graph, _ = build_c_graph(args.repo_path, fuzzy_search=args.fuzzy_search)

        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")

        # Print statistics
        node_types = []
        edge_types = []

        for node, data in graph.nodes(data=True):
            node_types.append(data.get('type', 'unknown'))

        for _, _, data in graph.edges(data=True):
            edge_types.append(data.get('type', 'unknown'))

        print("\nNode type distribution:")
        node_counter = Counter(node_types)
        for node_type, count in node_counter.items():
            print(f"  {node_type}: {count}")

        print("\nEdge type distribution:")
        edge_counter = Counter(edge_types)
        for edge_type, count in edge_counter.items():
            print(f"  {edge_type}: {count}")

        # Print directory structure
        print("\nProject structure:")
        traverse_c_directory_structure(graph)

        # Generate visualization if requested
        if args.visualize:
            print(f"\nGenerating visualization...")
            visualize_c_graph(graph)
            print(f"Visualization saved to {args.output_dir}/c_dependency_graph.png")

        # Save graph for further analysis (optional)
        import pickle
        with open(f"{args.output_dir}/c_graph.pkl", "wb") as f:
            pickle.dump(graph, f)
        print(f"Graph saved to {args.output_dir}/c_graph.pkl")

    except Exception as e:
        print(f"Error building graph: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
