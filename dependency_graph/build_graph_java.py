import argparse
import os
import re
import chardet
from collections import Counter, defaultdict
from typing import List, Dict, Set

import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from tree_sitter_languages import get_language
from tree_sitter import Language, Node, Parser

VERSION = 'v1.0'
NODE_TYPE_DIRECTORY = 'directory'
NODE_TYPE_FILE = 'file'
NODE_TYPE_CLASS = 'class'
NODE_TYPE_METHOD = 'method'
NODE_TYPE_INTERFACE = 'interface'
NODE_TYPE_ENUM = 'enum'
EDGE_TYPE_CONTAINS = 'contains'
EDGE_TYPE_INHERITS = 'inherits'
EDGE_TYPE_INVOKES = 'invokes'
EDGE_TYPE_IMPORTS = 'imports'
EDGE_TYPE_IMPLEMENTS = 'implements'

VALID_NODE_TYPES = [NODE_TYPE_DIRECTORY, NODE_TYPE_FILE, NODE_TYPE_CLASS, NODE_TYPE_METHOD, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]
VALID_EDGE_TYPES = [EDGE_TYPE_CONTAINS, EDGE_TYPE_INHERITS, EDGE_TYPE_INVOKES, EDGE_TYPE_IMPORTS, EDGE_TYPE_IMPLEMENTS]

SKIP_DIRS = ['.github', '.git', 'target', 'build', '.idea', '.vscode']

def is_skip_dir(dirname):
    for skip_dir in SKIP_DIRS:
        if skip_dir in dirname:
            return True
    return False


class JavaCodeAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.nodes = []
        self.imports = []
        self.parser = Parser()
        self.parser.set_language(get_language('java'))
        
    def analyze(self, content):
        """Analyze Java file content using tree-sitter"""
        tree = self.parser.parse(content.encode('utf-8'))
        self._traverse_node(tree.root_node, content)
        return self.nodes, self.imports
    
    def _traverse_node(self, node: Node, content: str, parent_name: str = ""):
        """Traverse tree-sitter AST nodes"""
        if node.type == 'class_declaration':
            self._handle_class(node, content, parent_name)
        elif node.type == 'interface_declaration':
            self._handle_interface(node, content, parent_name)
        elif node.type == 'enum_declaration':
            self._handle_enum(node, content, parent_name)
        elif node.type == 'method_declaration':
            self._handle_method(node, content, parent_name)
        elif node.type == 'constructor_declaration':
            self._handle_constructor(node, content, parent_name)
        elif node.type == 'import_declaration':
            self._handle_import(node, content)
        
        # Recursively traverse child nodes
        for child in node.children:
            current_parent = parent_name
            if node.type in ['class_declaration', 'interface_declaration', 'enum_declaration']:
                class_name = self._get_identifier(node)
                if class_name:
                    current_parent = f"{parent_name}.{class_name}" if parent_name else class_name
            self._traverse_node(child, content, current_parent)
    
    def _get_identifier(self, node: Node) -> str:
        """Extract identifier from a node"""
        for child in node.children:
            if child.type == 'identifier':
                return child.text.decode('utf-8')
        return ""
    
    def _get_source_segment(self, node: Node, content: str) -> str:
        """Get source code segment for a node"""
        start_byte = node.start_byte
        end_byte = node.end_byte
        return content.encode('utf-8')[start_byte:end_byte].decode('utf-8')
    
    def _handle_class(self, node: Node, content: str, parent_name: str):
        """Handle class declaration"""
        class_name = self._get_identifier(node)
        if class_name:
            full_name = f"{parent_name}.{class_name}" if parent_name else class_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_CLASS,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'extends': self._get_extends(node),
                'implements': self._get_implements(node)
            })
    
    def _handle_interface(self, node: Node, content: str, parent_name: str):
        """Handle interface declaration"""
        interface_name = self._get_identifier(node)
        if interface_name:
            full_name = f"{parent_name}.{interface_name}" if parent_name else interface_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_INTERFACE,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'extends': self._get_extends(node)
            })
    
    def _handle_enum(self, node: Node, content: str, parent_name: str):
        """Handle enum declaration"""
        enum_name = self._get_identifier(node)
        if enum_name:
            full_name = f"{parent_name}.{enum_name}" if parent_name else enum_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_ENUM,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'implements': self._get_implements(node)
            })
    
    def _handle_method(self, node: Node, content: str, parent_name: str):
        """Handle method declaration"""
        method_name = self._get_identifier(node)
        if method_name:
            full_name = f"{parent_name}.{method_name}" if parent_name else method_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_METHOD,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'invocations': self._get_method_invocations(node)
            })
    
    def _handle_constructor(self, node: Node, content: str, parent_name: str):
        """Handle constructor declaration"""
        constructor_name = self._get_identifier(node)
        if constructor_name:
            full_name = f"{parent_name}.{constructor_name}" if parent_name else constructor_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_METHOD,  # Treat constructor as method
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'invocations': self._get_method_invocations(node),
                'is_constructor': True
            })
    
    def _handle_import(self, node: Node, content: str):
        """Handle import declaration"""
        import_text = self._get_source_segment(node, content).strip()
        # Extract import path from "import package.Class;" or "import package.*;"
        import_match = re.match(r'import\s+(static\s+)?([^;]+);', import_text)
        if import_match:
            is_static = import_match.group(1) is not None
            import_path = import_match.group(2).strip()
            self.imports.append({
                'path': import_path,
                'is_static': is_static,
                'is_wildcard': import_path.endswith('*')
            })
    
    def _get_extends(self, node: Node) -> List[str]:
        """Get extends clause from class/interface declaration"""
        extends = []
        for child in node.children:
            if child.type == 'superclass':
                for grandchild in child.children:
                    if grandchild.type == 'type_identifier':
                        extends.append(grandchild.text.decode('utf-8'))
            elif child.type == 'super_interfaces':
                for grandchild in child.children:
                    if grandchild.type == 'type_list':
                        for type_node in grandchild.children:
                            if type_node.type == 'type_identifier':
                                extends.append(type_node.text.decode('utf-8'))
        return extends
    
    def _get_implements(self, node: Node) -> List[str]:
        """Get implements clause from class declaration"""
        implements = []
        for child in node.children:
            if child.type == 'super_interfaces':
                for grandchild in child.children:
                    if grandchild.type == 'type_list':
                        for type_node in grandchild.children:
                            if type_node.type == 'type_identifier':
                                implements.append(type_node.text.decode('utf-8'))
        return implements
    
    def _get_method_invocations(self, node: Node) -> List[str]:
        """Get method invocations from method body"""
        invocations = []
        self._find_method_calls(node, invocations)
        return invocations
    
    def _find_method_calls(self, node: Node, invocations: List[str]):
        """Recursively find method calls in AST"""
        if node.type == 'method_invocation':
            # Extract method name from method_invocation
            for child in node.children:
                if child.type == 'identifier':
                    invocations.append(child.text.decode('utf-8'))
                elif child.type == 'field_access':
                    # Handle chained method calls like obj.method()
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            invocations.append(grandchild.text.decode('utf-8'))
        
        # Recursively search in children
        for child in node.children:
            self._find_method_calls(child, invocations)


def read_file_safely(filepath: str) -> str:
    """Safely read file with encoding detection"""
    with open(filepath, "rb") as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        encoding = result["encoding"] or "utf-8"
    
    with open(filepath, "r", encoding=encoding) as f:
        return f.read()


def analyze_java_file(filepath):
    """Analyze a Java file and extract classes, methods, imports"""
    try:
        content = read_file_safely(filepath)
    except:
        raise UnicodeDecodeError
    
    try:
        analyzer = JavaCodeAnalyzer(filepath)
        nodes, imports = analyzer.analyze(content)
        return nodes, imports
    except Exception as e:
        raise SyntaxError(f"Failed to parse {filepath}: {e}")


def resolve_java_class(class_name, package_name, repo_path):
    """
    Resolve a Java class name to a file path in the repo.
    Returns the file path if found, or None if not found.
    """
    # Try full qualified name first
    if '.' in class_name:
        class_path = os.path.join(repo_path, class_name.replace('.', '/') + '.java')
        if os.path.isfile(class_path):
            return class_path
    
    # Try with current package
    if package_name:
        full_class_name = f"{package_name}.{class_name}"
        class_path = os.path.join(repo_path, full_class_name.replace('.', '/') + '.java')
        if os.path.isfile(class_path):
            return class_path
    
    # Try to find in any package (fuzzy search)
    for root, dirs, files in os.walk(repo_path):
        if f"{class_name}.java" in files:
            return os.path.join(root, f"{class_name}.java")
    
    return None


def extract_package_name(filepath, repo_path):
    """Extract package name from Java file"""
    try:
        content = read_file_safely(filepath)
        package_match = re.search(r'package\s+([^;]+);', content)
        if package_match:
            return package_match.group(1).strip()
    except:
        pass
    return None


def add_java_imports(root_node, imports, graph, repo_path, package_name):
    """Add import edges to the graph"""
    for imp in imports:
        import_path = imp['path']

        if imp['is_wildcard']:
            # Handle wildcard imports like java.util.*
            continue  # Skip wildcard imports for now

        # Try to resolve the imported class
        if '.' in import_path:
            # Full qualified import
            class_name = import_path.split('.')[-1]
            class_file_path = resolve_java_class(class_name, None, repo_path)
        else:
            # Simple class name
            class_file_path = resolve_java_class(import_path, package_name, repo_path)

        if class_file_path:
            imp_filename = os.path.relpath(class_file_path, repo_path)
            if graph.has_node(imp_filename):
                graph.add_edge(root_node, imp_filename, type=EDGE_TYPE_IMPORTS)

            # Also try to link to specific class within the file
            class_node = f"{imp_filename}:{class_name if '.' in import_path else import_path}"
            if graph.has_node(class_node):
                graph.add_edge(root_node, class_node, type=EDGE_TYPE_IMPORTS)


def build_java_graph(repo_path, fuzzy_search=True):
    """
    Build dependency graph for Java files in the repository
    """
    graph = nx.MultiDiGraph()
    file_nodes = {}

    # Add root directory node
    graph.add_node('/', type=NODE_TYPE_DIRECTORY)
    dir_stack: List[str] = []
    dir_include_stack: List[bool] = []

    # First pass: Add nodes (directories, files, classes, methods)
    for root, _, files in os.walk(repo_path):
        # Add directory nodes and edges
        dirname = os.path.relpath(root, repo_path)
        if dirname == '.':
            dirname = '/'
        elif is_skip_dir(dirname):
            continue
        else:
            graph.add_node(dirname, type=NODE_TYPE_DIRECTORY)
            parent_dirname = os.path.dirname(dirname)
            if parent_dirname == '':
                parent_dirname = '/'
            graph.add_edge(parent_dirname, dirname, type=EDGE_TYPE_CONTAINS)

        # Remove directories that don't contain Java files
        while len(dir_stack) > 0 and not dirname.startswith(dir_stack[-1]):
            if not dir_include_stack[-1]:
                graph.remove_node(dir_stack[-1])
            dir_stack.pop()
            dir_include_stack.pop()
        if dirname != '/':
            dir_stack.append(dirname)
            dir_include_stack.append(False)

        dir_has_java = False
        for file in files:
            if file.endswith('.java'):
                dir_has_java = True

                # Add file nodes
                try:
                    file_path = os.path.join(root, file)
                    filename = os.path.relpath(file_path, repo_path)

                    if os.path.islink(file_path):
                        continue

                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    graph.add_node(filename, type=NODE_TYPE_FILE, code=file_content)

                    # Extract package name
                    package_name = extract_package_name(file_path, repo_path)

                    # Analyze Java file
                    nodes, imports = analyze_java_file(file_path)

                except (UnicodeDecodeError, SyntaxError) as e:
                    print(f"Skipping file {file_path}: {e}")
                    continue

                # Add class/method/interface nodes
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    node_attrs = {
                        'type': node['type'],
                        'code': node['code'],
                        'start_line': node['start_line'],
                        'end_line': node['end_line']
                    }

                    # Add type-specific attributes
                    if 'extends' in node:
                        node_attrs['extends'] = node['extends']
                    if 'implements' in node:
                        node_attrs['implements'] = node['implements']
                    if 'invocations' in node:
                        node_attrs['invocations'] = node['invocations']
                    if 'is_constructor' in node:
                        node_attrs['is_constructor'] = node['is_constructor']

                    graph.add_node(full_name, **node_attrs)

                # Add containment edges
                graph.add_edge(dirname, filename, type=EDGE_TYPE_CONTAINS)
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    name_parts = node['name'].split('.')
                    if len(name_parts) == 1:
                        # Top-level class/interface
                        graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)
                    else:
                        # Nested class/method
                        parent_name = '.'.join(name_parts[:-1])
                        full_parent_name = f'{filename}:{parent_name}'
                        if graph.has_node(full_parent_name):
                            graph.add_edge(full_parent_name, full_name, type=EDGE_TYPE_CONTAINS)
                        else:
                            # Fallback to file containment
                            graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)

                # Store imports for later processing
                file_nodes[filename] = {
                    'path': file_path,
                    'imports': imports,
                    'package': package_name
                }

        # Keep all parent directories that contain Java files
        if dir_has_java:
            for i in range(len(dir_include_stack)):
                dir_include_stack[i] = True

    # Clean up remaining directories
    while len(dir_stack) > 0:
        if not dir_include_stack[-1]:
            graph.remove_node(dir_stack[-1])
        dir_stack.pop()
        dir_include_stack.pop()

    # Second pass: Add import, inheritance, implementation, and invocation edges
    for filename, file_info in file_nodes.items():
        if isinstance(file_info, dict):
            file_path = file_info['path']
            imports = file_info['imports']
            package_name = file_info['package']

            # Add import edges
            add_java_imports(filename, imports, graph, repo_path, package_name)

    # Third pass: Add inheritance, implementation, and method invocation edges
    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') not in [NODE_TYPE_CLASS, NODE_TYPE_METHOD, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]:
            continue

        # Handle inheritance (extends)
        if 'extends' in attributes:
            for parent_class in attributes['extends']:
                # Find the parent class node
                parent_nodes = find_class_nodes(graph, parent_class, fuzzy_search)
                for parent_node in parent_nodes:
                    graph.add_edge(node, parent_node, type=EDGE_TYPE_INHERITS)

        # Handle implementation (implements)
        if 'implements' in attributes:
            for interface in attributes['implements']:
                # Find the interface node
                interface_nodes = find_class_nodes(graph, interface, fuzzy_search)
                for interface_node in interface_nodes:
                    graph.add_edge(node, interface_node, type=EDGE_TYPE_IMPLEMENTS)

        # Handle method invocations
        if 'invocations' in attributes:
            for method_name in attributes['invocations']:
                # Find possible method nodes
                method_nodes = find_method_nodes(graph, method_name, fuzzy_search)
                for method_node in method_nodes:
                    graph.add_edge(node, method_node, type=EDGE_TYPE_INVOKES)

    return graph


def find_class_nodes(graph, class_name, fuzzy_search=True):
    """Find class/interface/enum nodes by name"""
    matching_nodes = []

    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') in [NODE_TYPE_CLASS, NODE_TYPE_INTERFACE, NODE_TYPE_ENUM]:
            node_name = node.split(':')[-1] if ':' in node else node

            if fuzzy_search:
                # Match by simple class name
                if node_name.split('.')[-1] == class_name:
                    matching_nodes.append(node)
            else:
                # Exact match
                if node_name == class_name:
                    matching_nodes.append(node)

    return matching_nodes


def find_method_nodes(graph, method_name, fuzzy_search=True):
    """Find method nodes by name"""
    matching_nodes = []

    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') == NODE_TYPE_METHOD:
            node_name = node.split(':')[-1] if ':' in node else node

            if fuzzy_search:
                # Match by simple method name
                if node_name.split('.')[-1] == method_name:
                    matching_nodes.append(node)
            else:
                # Exact match
                if node_name == method_name:
                    matching_nodes.append(node)

    return matching_nodes


def visualize_java_graph(G):
    """Visualize the Java dependency graph"""
    node_types = set(nx.get_node_attributes(G, 'type').values())
    node_shapes = {
        NODE_TYPE_CLASS: 'o',
        NODE_TYPE_METHOD: 's',
        NODE_TYPE_FILE: 'D',
        NODE_TYPE_DIRECTORY: '^',
        NODE_TYPE_INTERFACE: 'v',
        NODE_TYPE_ENUM: 'p'
    }
    node_colors = {
        NODE_TYPE_CLASS: 'lightgreen',
        NODE_TYPE_METHOD: 'lightblue',
        NODE_TYPE_FILE: 'lightgrey',
        NODE_TYPE_DIRECTORY: 'orange',
        NODE_TYPE_INTERFACE: 'lightcoral',
        NODE_TYPE_ENUM: 'lightyellow'
    }

    edge_types = set(nx.get_edge_attributes(G, 'type').values())
    edge_colors = {
        EDGE_TYPE_IMPORTS: 'forestgreen',
        EDGE_TYPE_CONTAINS: 'skyblue',
        EDGE_TYPE_INVOKES: 'magenta',
        EDGE_TYPE_INHERITS: 'brown',
        EDGE_TYPE_IMPLEMENTS: 'purple'
    }
    edge_styles = {
        EDGE_TYPE_IMPORTS: 'solid',
        EDGE_TYPE_CONTAINS: 'dashed',
        EDGE_TYPE_INVOKES: 'dotted',
        EDGE_TYPE_INHERITS: 'dashdot',
        EDGE_TYPE_IMPLEMENTS: (0, (3, 1, 1, 1))
    }

    pos = nx.spring_layout(G, k=2, iterations=50)

    plt.figure(figsize=(20, 20))
    plt.margins(0.15)

    # Draw nodes with different shapes and colors based on their type
    for ntype in node_types:
        nodelist = [n for n, d in G.nodes(data=True) if d['type'] == ntype]
        if nodelist:  # Only draw if there are nodes of this type
            nx.draw_networkx_nodes(
                G,
                pos,
                nodelist=nodelist,
                node_shape=node_shapes.get(ntype, 'o'),
                node_color=node_colors.get(ntype, 'lightgray'),
                node_size=700,
                label=ntype,
            )

    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=8, font_family='sans-serif')

    # Group edges between the same pair of nodes
    edge_groups = {}
    for u, v, key, data in G.edges(keys=True, data=True):
        if (u, v) not in edge_groups:
            edge_groups[(u, v)] = []
        edge_groups[(u, v)].append((key, data))

    # Draw edges with adjusted 'rad' values
    for (u, v), edges in edge_groups.items():
        num_edges = len(edges)
        for i, (key, data) in enumerate(edges):
            edge_type = data['type']
            # Adjust 'rad' to spread the edges
            rad = 0.1 * (i - (num_edges - 1) / 2)
            nx.draw_networkx_edges(
                G,
                pos,
                edgelist=[(u, v)],
                edge_color=edge_colors.get(edge_type, 'black'),
                style=edge_styles.get(edge_type, 'solid'),
                connectionstyle=f'arc3,rad={rad}',
                arrows=True,
                arrowstyle='-|>',
                arrowsize=15,
                min_source_margin=15,
                min_target_margin=15,
                width=1.5
            )

    # Create legends for edge types and node types
    edge_legend_elements = [
        Line2D([0], [0], color=edge_colors.get(etype, 'black'), lw=2,
               linestyle=edge_styles.get(etype, 'solid'), label=etype)
        for etype in edge_types
    ]
    node_legend_elements = [
        Line2D([0], [0], marker=node_shapes.get(ntype, 'o'), color='w', label=ntype,
               markerfacecolor=node_colors.get(ntype, 'lightgray'), markersize=15)
        for ntype in node_types
    ]

    # Combine legends
    plt.legend(handles=edge_legend_elements + node_legend_elements, loc='upper left')
    plt.axis('off')
    plt.savefig('plots/java_dependency_graph.png', dpi=300, bbox_inches='tight')
    plt.show()


def traverse_java_directory_structure(graph, root='/'):
    """Traverse and print the directory structure of the Java project"""
    def traverse(node, prefix, is_last):
        if node == root:
            print(f"{node}")
            new_prefix = ''
        else:
            connector = '└── ' if is_last else '├── '
            print(f"{prefix}{connector}{node}")
            new_prefix = prefix + ('    ' if is_last else '│   ')

        # Stop if the current node is a file (leaf node)
        if graph.nodes[node].get('type') == NODE_TYPE_FILE:
            return

        # Traverse neighbors with edge type 'contains'
        neighbors = list(graph.neighbors(node))
        for i, neighbor in enumerate(neighbors):
            for key in graph[node][neighbor]:
                if graph[node][neighbor][key].get('type') == EDGE_TYPE_CONTAINS:
                    is_last_child = (i == len(neighbors) - 1)
                    traverse(neighbor, new_prefix, is_last_child)

    traverse(root, '', False)


def main():
    """Main function to build and analyze Java dependency graph"""
    parser = argparse.ArgumentParser(description='Build dependency graph for Java projects')
    parser.add_argument('--repo_path', type=str, required=True,
                        help='Path to the Java repository root')
    parser.add_argument('--visualize', action='store_true',
                        help='Generate visualization of the dependency graph')
    parser.add_argument('--fuzzy_search', action='store_true', default=True,
                        help='Enable fuzzy search for class and method matching')
    parser.add_argument('--output_dir', type=str, default='plots',
                        help='Directory to save visualization plots')

    args = parser.parse_args()

    # Ensure output directory exists
    if args.visualize:
        os.makedirs(args.output_dir, exist_ok=True)

    print(f"Building Java dependency graph for: {args.repo_path}")

    try:
        # Build the dependency graph
        graph = build_java_graph(args.repo_path, fuzzy_search=args.fuzzy_search)

        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")

        # Print statistics
        node_types = []
        edge_types = []

        for node, data in graph.nodes(data=True):
            node_types.append(data.get('type', 'unknown'))

        for _, _, data in graph.edges(data=True):
            edge_types.append(data.get('type', 'unknown'))

        print("\nNode type distribution:")
        node_counter = Counter(node_types)
        for node_type, count in node_counter.items():
            print(f"  {node_type}: {count}")

        print("\nEdge type distribution:")
        edge_counter = Counter(edge_types)
        for edge_type, count in edge_counter.items():
            print(f"  {edge_type}: {count}")

        # Print directory structure
        print("\nProject structure:")
        traverse_java_directory_structure(graph)

        # Generate visualization if requested
        if args.visualize:
            print(f"\nGenerating visualization...")
            visualize_java_graph(graph)
            print(f"Visualization saved to {args.output_dir}/java_dependency_graph.png")

        # Save graph for further analysis (optional)
        import pickle
        with open(f"{args.output_dir}/java_graph.pkl", "wb") as f:
            pickle.dump(graph, f)
        print(f"Graph saved to {args.output_dir}/java_graph.pkl")

    except Exception as e:
        print(f"Error building graph: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
