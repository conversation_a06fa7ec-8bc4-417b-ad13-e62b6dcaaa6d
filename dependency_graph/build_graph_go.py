import argparse
import os
import re
import chardet
from collections import Counter, defaultdict
from typing import List, Dict, Set

import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from tree_sitter_languages import get_language
from tree_sitter import Language, Node, Parser

VERSION = 'v1.0'
NODE_TYPE_DIRECTORY = 'directory'
NODE_TYPE_FILE = 'file'
NODE_TYPE_PACKAGE = 'package'
NODE_TYPE_STRUCT = 'struct'
NODE_TYPE_INTERFACE = 'interface'
NODE_TYPE_FUNCTION = 'function'
NODE_TYPE_METHOD = 'method'
NODE_TYPE_VARIABLE = 'variable'
NODE_TYPE_CONSTANT = 'constant'
EDGE_TYPE_CONTAINS = 'contains'
EDGE_TYPE_INVOKES = 'invokes'
EDGE_TYPE_IMPORTS = 'imports'
EDGE_TYPE_IMPLEMENTS = 'implements'

VALID_NODE_TYPES = [NODE_TYPE_DIRECTORY, NODE_TYPE_FILE, NODE_TYPE_PACKAGE, NODE_TYPE_STRUCT, 
                   NODE_TYPE_INTERFACE, NODE_TYPE_FUNCTION, NODE_TYPE_METHOD, NODE_TYPE_VARIABLE, NODE_TYPE_CONSTANT]
VALID_EDGE_TYPES = [EDGE_TYPE_CONTAINS, EDGE_TYPE_INVOKES, EDGE_TYPE_IMPORTS, EDGE_TYPE_IMPLEMENTS]

SKIP_DIRS = ['.github', '.git', 'vendor', '.idea', '.vscode', 'node_modules']

def is_skip_dir(dirname):
    for skip_dir in SKIP_DIRS:
        if skip_dir in dirname:
            return True
    return False


class GoCodeAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.nodes = []
        self.imports = []
        self.package_name = ""
        self.parser = Parser()
        self.parser.set_language(get_language('go'))
        
    def analyze(self, content):
        """Analyze Go file content using tree-sitter"""
        tree = self.parser.parse(content.encode('utf-8'))
        self._traverse_node(tree.root_node, content)
        return self.nodes, self.imports, self.package_name
    
    def _traverse_node(self, node: Node, content: str, parent_name: str = ""):
        """Traverse tree-sitter AST nodes"""
        if node.type == 'package_clause':
            self._handle_package(node, content)
        elif node.type == 'import_declaration':
            self._handle_import(node, content)
        elif node.type == 'type_declaration':
            self._handle_type_declaration(node, content, parent_name)
        elif node.type == 'function_declaration':
            self._handle_function(node, content, parent_name)
        elif node.type == 'method_declaration':
            self._handle_method(node, content, parent_name)
        elif node.type == 'var_declaration':
            self._handle_variable(node, content, parent_name)
        elif node.type == 'const_declaration':
            self._handle_constant(node, content, parent_name)
        
        # Recursively traverse child nodes
        for child in node.children:
            current_parent = parent_name
            if node.type in ['type_declaration']:
                type_name = self._get_type_identifier(node)
                if type_name:
                    current_parent = f"{parent_name}.{type_name}" if parent_name else type_name
            self._traverse_node(child, content, current_parent)
    
    def _get_identifier(self, node: Node) -> str:
        """Extract identifier from a node"""
        for child in node.children:
            if child.type == 'identifier':
                return child.text.decode('utf-8')
        return ""
    
    def _get_type_identifier(self, node: Node) -> str:
        """Extract type identifier from a type declaration"""
        for child in node.children:
            if child.type == 'type_spec':
                for grandchild in child.children:
                    if grandchild.type == 'type_identifier':
                        return grandchild.text.decode('utf-8')
        return ""
    
    def _get_source_segment(self, node: Node, content: str) -> str:
        """Get source code segment for a node"""
        start_byte = node.start_byte
        end_byte = node.end_byte
        return content.encode('utf-8')[start_byte:end_byte].decode('utf-8')
    
    def _handle_package(self, node: Node, content: str):
        """Handle package declaration"""
        for child in node.children:
            if child.type == 'package_identifier':
                self.package_name = child.text.decode('utf-8')
                break
    
    def _handle_import(self, node: Node, content: str):
        """Handle import declaration"""
        import_text = self._get_source_segment(node, content).strip()
        
        # Extract import paths from various import formats
        import_paths = []
        
        # Handle single import: import "fmt"
        single_match = re.search(r'import\s+"([^"]+)"', import_text)
        if single_match:
            import_paths.append(single_match.group(1))
        
        # Handle multiple imports: import ( "fmt" "os" )
        multi_matches = re.findall(r'"([^"]+)"', import_text)
        if multi_matches and not single_match:
            import_paths.extend(multi_matches)
        
        for import_path in import_paths:
            # Handle aliased imports like: import alias "package"
            alias_match = re.search(r'(\w+)\s+"' + re.escape(import_path) + '"', import_text)
            alias = alias_match.group(1) if alias_match else None
            
            self.imports.append({
                'path': import_path,
                'alias': alias,
                'is_dot_import': import_path.startswith('.'),
                'is_blank_import': alias == '_'
            })
    
    def _handle_type_declaration(self, node: Node, content: str, parent_name: str):
        """Handle type declaration (struct, interface, etc.)"""
        type_name = self._get_type_identifier(node)
        if not type_name:
            return
            
        full_name = f"{parent_name}.{type_name}" if parent_name else type_name
        
        # Determine the type of declaration
        node_type = NODE_TYPE_STRUCT  # default
        for child in node.children:
            if child.type == 'type_spec':
                for grandchild in child.children:
                    if grandchild.type == 'struct_type':
                        node_type = NODE_TYPE_STRUCT
                        break
                    elif grandchild.type == 'interface_type':
                        node_type = NODE_TYPE_INTERFACE
                        break
        
        self.nodes.append({
            'name': full_name,
            'type': node_type,
            'code': self._get_source_segment(node, content),
            'start_line': node.start_point[0] + 1,
            'end_line': node.end_point[0] + 1,
            'methods': self._get_struct_methods(node) if node_type == NODE_TYPE_STRUCT else []
        })
    
    def _handle_function(self, node: Node, content: str, parent_name: str):
        """Handle function declaration"""
        func_name = self._get_identifier(node)
        if func_name:
            full_name = f"{parent_name}.{func_name}" if parent_name else func_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_FUNCTION,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'invocations': self._get_function_calls(node)
            })
    
    def _handle_method(self, node: Node, content: str, parent_name: str):
        """Handle method declaration"""
        method_name = ""
        receiver_type = ""
        
        # Extract method name and receiver type
        for child in node.children:
            if child.type == 'field_identifier':
                method_name = child.text.decode('utf-8')
            elif child.type == 'parameter_list' and not receiver_type:
                # This is the receiver
                receiver_type = self._extract_receiver_type(child)
        
        if method_name:
            full_name = f"{receiver_type}.{method_name}" if receiver_type else method_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_METHOD,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1,
                'receiver_type': receiver_type,
                'invocations': self._get_function_calls(node)
            })
    
    def _handle_variable(self, node: Node, content: str, parent_name: str):
        """Handle variable declaration"""
        var_names = self._extract_var_names(node)
        for var_name in var_names:
            full_name = f"{parent_name}.{var_name}" if parent_name else var_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_VARIABLE,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1
            })
    
    def _handle_constant(self, node: Node, content: str, parent_name: str):
        """Handle constant declaration"""
        const_names = self._extract_const_names(node)
        for const_name in const_names:
            full_name = f"{parent_name}.{const_name}" if parent_name else const_name
            self.nodes.append({
                'name': full_name,
                'type': NODE_TYPE_CONSTANT,
                'code': self._get_source_segment(node, content),
                'start_line': node.start_point[0] + 1,
                'end_line': node.end_point[0] + 1
            })
    
    def _extract_receiver_type(self, param_list: Node) -> str:
        """Extract receiver type from parameter list"""
        for child in param_list.children:
            if child.type == 'parameter_declaration':
                for grandchild in child.children:
                    if grandchild.type == 'type_identifier':
                        return grandchild.text.decode('utf-8')
                    elif grandchild.type == 'pointer_type':
                        for ggchild in grandchild.children:
                            if ggchild.type == 'type_identifier':
                                return ggchild.text.decode('utf-8')
        return ""
    
    def _extract_var_names(self, node: Node) -> List[str]:
        """Extract variable names from var declaration"""
        names = []
        for child in node.children:
            if child.type == 'var_spec':
                for grandchild in child.children:
                    if grandchild.type == 'identifier':
                        names.append(grandchild.text.decode('utf-8'))
        return names
    
    def _extract_const_names(self, node: Node) -> List[str]:
        """Extract constant names from const declaration"""
        names = []
        for child in node.children:
            if child.type == 'const_spec':
                for grandchild in child.children:
                    if grandchild.type == 'identifier':
                        names.append(grandchild.text.decode('utf-8'))
        return names
    
    def _get_struct_methods(self, node: Node) -> List[str]:
        """Get methods associated with a struct (placeholder)"""
        # This would require more complex analysis to find methods with this struct as receiver
        return []
    
    def _get_function_calls(self, node: Node) -> List[str]:
        """Get function calls from function/method body"""
        calls = []
        self._find_function_calls(node, calls)
        return calls
    
    def _find_function_calls(self, node: Node, calls: List[str]):
        """Recursively find function calls in AST"""
        if node.type == 'call_expression':
            # Extract function name from call_expression
            for child in node.children:
                if child.type == 'identifier':
                    calls.append(child.text.decode('utf-8'))
                elif child.type == 'selector_expression':
                    # Handle method calls like obj.method()
                    for grandchild in child.children:
                        if grandchild.type == 'field_identifier':
                            calls.append(grandchild.text.decode('utf-8'))
        
        # Recursively search in children
        for child in node.children:
            self._find_function_calls(child, calls)


def read_file_safely(filepath: str) -> str:
    """Safely read file with encoding detection"""
    with open(filepath, "rb") as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        encoding = result["encoding"] or "utf-8"

    with open(filepath, "r", encoding=encoding) as f:
        return f.read()


def analyze_go_file(filepath):
    """Analyze a Go file and extract structs, functions, imports"""
    try:
        content = read_file_safely(filepath)
    except:
        raise UnicodeDecodeError

    try:
        analyzer = GoCodeAnalyzer(filepath)
        nodes, imports, package_name = analyzer.analyze(content)
        return nodes, imports, package_name
    except Exception as e:
        raise SyntaxError(f"Failed to parse {filepath}: {e}")


def resolve_go_package(import_path, repo_path):
    """
    Resolve a Go import path to a directory in the repo.
    Returns the directory path if found, or None if not found.
    """
    # Handle standard library imports
    if not '.' in import_path and '/' not in import_path:
        return None  # Standard library

    # Handle relative imports
    if import_path.startswith('./') or import_path.startswith('../'):
        return None  # Relative imports need context

    # Try to find the package in the repo
    # Go packages are typically organized by import path
    package_path = os.path.join(repo_path, import_path)
    if os.path.isdir(package_path):
        return package_path

    # Try to find in vendor directory
    vendor_path = os.path.join(repo_path, 'vendor', import_path)
    if os.path.isdir(vendor_path):
        return vendor_path

    # Try to find anywhere in the repo (fuzzy search)
    package_name = import_path.split('/')[-1]
    for root, _, files in os.walk(repo_path):
        if os.path.basename(root) == package_name:
            # Check if it contains Go files
            for file in files:
                if file.endswith('.go'):
                    return root

    return None


def extract_go_module_name(repo_path):
    """Extract module name from go.mod file"""
    go_mod_path = os.path.join(repo_path, 'go.mod')
    if os.path.exists(go_mod_path):
        try:
            with open(go_mod_path, 'r') as f:
                content = f.read()
                module_match = re.search(r'module\s+([^\s\n]+)', content)
                if module_match:
                    return module_match.group(1)
        except:
            pass
    return None


def add_go_imports(root_node, imports, graph, repo_path, module_name):
    """Add import edges to the graph"""
    for imp in imports:
        import_path = imp['path']

        # Skip standard library imports
        if not '.' in import_path and '/' not in import_path:
            continue

        # Try to resolve the imported package
        package_dir = resolve_go_package(import_path, repo_path)

        if package_dir:
            # Find Go files in the package directory
            for file in os.listdir(package_dir):
                if file.endswith('.go'):
                    file_path = os.path.join(package_dir, file)
                    imp_filename = os.path.relpath(file_path, repo_path)
                    if graph.has_node(imp_filename):
                        graph.add_edge(root_node, imp_filename, type=EDGE_TYPE_IMPORTS)


def build_go_graph(repo_path, fuzzy_search=True):
    """
    Build dependency graph for Go files in the repository
    """
    graph = nx.MultiDiGraph()
    file_nodes = {}

    # Extract module name from go.mod
    module_name = extract_go_module_name(repo_path)

    # Add root directory node
    graph.add_node('/', type=NODE_TYPE_DIRECTORY)
    dir_stack: List[str] = []
    dir_include_stack: List[bool] = []

    # First pass: Add nodes (directories, files, structs, functions)
    for root, _, files in os.walk(repo_path):
        # Add directory nodes and edges
        dirname = os.path.relpath(root, repo_path)
        if dirname == '.':
            dirname = '/'
        elif is_skip_dir(dirname):
            continue
        else:
            graph.add_node(dirname, type=NODE_TYPE_DIRECTORY)
            parent_dirname = os.path.dirname(dirname)
            if parent_dirname == '':
                parent_dirname = '/'
            graph.add_edge(parent_dirname, dirname, type=EDGE_TYPE_CONTAINS)

        # Remove directories that don't contain Go files
        while len(dir_stack) > 0 and not dirname.startswith(dir_stack[-1]):
            if not dir_include_stack[-1]:
                graph.remove_node(dir_stack[-1])
            dir_stack.pop()
            dir_include_stack.pop()
        if dirname != '/':
            dir_stack.append(dirname)
            dir_include_stack.append(False)

        dir_has_go = False
        for file in files:
            if file.endswith('.go') and not file.endswith('_test.go'):  # Skip test files for now
                dir_has_go = True

                # Add file nodes
                try:
                    file_path = os.path.join(root, file)
                    filename = os.path.relpath(file_path, repo_path)

                    if os.path.islink(file_path):
                        continue

                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    graph.add_node(filename, type=NODE_TYPE_FILE, code=file_content)

                    # Analyze Go file
                    nodes, imports, package_name = analyze_go_file(file_path)

                except (UnicodeDecodeError, SyntaxError) as e:
                    print(f"Skipping file {file_path}: {e}")
                    continue

                # Add struct/function/method nodes
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    node_attrs = {
                        'type': node['type'],
                        'code': node['code'],
                        'start_line': node['start_line'],
                        'end_line': node['end_line']
                    }

                    # Add type-specific attributes
                    if 'methods' in node:
                        node_attrs['methods'] = node['methods']
                    if 'invocations' in node:
                        node_attrs['invocations'] = node['invocations']
                    if 'receiver_type' in node:
                        node_attrs['receiver_type'] = node['receiver_type']

                    graph.add_node(full_name, **node_attrs)

                # Add containment edges
                graph.add_edge(dirname, filename, type=EDGE_TYPE_CONTAINS)
                for node in nodes:
                    full_name = f'{filename}:{node["name"]}'
                    name_parts = node['name'].split('.')
                    if len(name_parts) == 1:
                        # Top-level declaration
                        graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)
                    else:
                        # Nested declaration
                        parent_name = '.'.join(name_parts[:-1])
                        full_parent_name = f'{filename}:{parent_name}'
                        if graph.has_node(full_parent_name):
                            graph.add_edge(full_parent_name, full_name, type=EDGE_TYPE_CONTAINS)
                        else:
                            # Fallback to file containment
                            graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)

                # Store imports for later processing
                file_nodes[filename] = {
                    'path': file_path,
                    'imports': imports,
                    'package': package_name
                }

        # Keep all parent directories that contain Go files
        if dir_has_go:
            for i in range(len(dir_include_stack)):
                dir_include_stack[i] = True

    # Clean up remaining directories
    while len(dir_stack) > 0:
        if not dir_include_stack[-1]:
            graph.remove_node(dir_stack[-1])
        dir_stack.pop()
        dir_include_stack.pop()

    # Second pass: Add import and invocation edges
    for filename, file_info in file_nodes.items():
        if isinstance(file_info, dict):
            file_path = file_info['path']
            imports = file_info['imports']
            package_name = file_info['package']

            # Add import edges
            add_go_imports(filename, imports, graph, repo_path, module_name)

    # Third pass: Add method invocation edges
    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') not in [NODE_TYPE_FUNCTION, NODE_TYPE_METHOD]:
            continue

        # Handle function/method invocations
        if 'invocations' in attributes:
            for func_name in attributes['invocations']:
                # Find possible function/method nodes
                target_nodes = find_function_nodes(graph, func_name, fuzzy_search)
                for target_node in target_nodes:
                    graph.add_edge(node, target_node, type=EDGE_TYPE_INVOKES)

    return graph, file_nodes


def find_function_nodes(graph, func_name, fuzzy_search=True):
    """Find function/method nodes by name"""
    matching_nodes = []

    for node, attributes in graph.nodes(data=True):
        if attributes.get('type') in [NODE_TYPE_FUNCTION, NODE_TYPE_METHOD]:
            node_name = node.split(':')[-1] if ':' in node else node

            if fuzzy_search:
                # Match by simple function name
                if node_name.split('.')[-1] == func_name:
                    matching_nodes.append(node)
            else:
                # Exact match
                if node_name == func_name:
                    matching_nodes.append(node)

    return matching_nodes


def visualize_go_graph(G):
    """Visualize the Go dependency graph"""
    node_types = set(nx.get_node_attributes(G, 'type').values())
    node_shapes = {
        NODE_TYPE_STRUCT: 'o',
        NODE_TYPE_FUNCTION: 's',
        NODE_TYPE_METHOD: '^',
        NODE_TYPE_FILE: 'D',
        NODE_TYPE_DIRECTORY: 'v',
        NODE_TYPE_INTERFACE: 'p',
        NODE_TYPE_PACKAGE: 'h',
        NODE_TYPE_VARIABLE: '*',
        NODE_TYPE_CONSTANT: '+'
    }
    node_colors = {
        NODE_TYPE_STRUCT: 'lightgreen',
        NODE_TYPE_FUNCTION: 'lightblue',
        NODE_TYPE_METHOD: 'lightcoral',
        NODE_TYPE_FILE: 'lightgrey',
        NODE_TYPE_DIRECTORY: 'orange',
        NODE_TYPE_INTERFACE: 'lightyellow',
        NODE_TYPE_PACKAGE: 'lightpink',
        NODE_TYPE_VARIABLE: 'lightcyan',
        NODE_TYPE_CONSTANT: 'lavender'
    }

    edge_types = set(nx.get_edge_attributes(G, 'type').values())
    edge_colors = {
        EDGE_TYPE_IMPORTS: 'forestgreen',
        EDGE_TYPE_CONTAINS: 'skyblue',
        EDGE_TYPE_INVOKES: 'magenta',
        EDGE_TYPE_IMPLEMENTS: 'purple'
    }
    edge_styles = {
        EDGE_TYPE_IMPORTS: 'solid',
        EDGE_TYPE_CONTAINS: 'dashed',
        EDGE_TYPE_INVOKES: 'dotted',
        EDGE_TYPE_IMPLEMENTS: (0, (3, 1, 1, 1))
    }

    pos = nx.spring_layout(G, k=2, iterations=50)

    plt.figure(figsize=(20, 20))
    plt.margins(0.15)

    # Draw nodes with different shapes and colors based on their type
    for ntype in node_types:
        nodelist = [n for n, d in G.nodes(data=True) if d['type'] == ntype]
        if nodelist:  # Only draw if there are nodes of this type
            nx.draw_networkx_nodes(
                G,
                pos,
                nodelist=nodelist,
                node_shape=node_shapes.get(ntype, 'o'),
                node_color=node_colors.get(ntype, 'lightgray'),
                node_size=700,
                label=ntype,
            )

    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=8, font_family='sans-serif')

    # Group edges between the same pair of nodes
    edge_groups = {}
    for u, v, key, data in G.edges(keys=True, data=True):
        if (u, v) not in edge_groups:
            edge_groups[(u, v)] = []
        edge_groups[(u, v)].append((key, data))

    # Draw edges with adjusted 'rad' values
    for (u, v), edges in edge_groups.items():
        num_edges = len(edges)
        for i, (key, data) in enumerate(edges):
            edge_type = data['type']
            # Adjust 'rad' to spread the edges
            rad = 0.1 * (i - (num_edges - 1) / 2)
            nx.draw_networkx_edges(
                G,
                pos,
                edgelist=[(u, v)],
                edge_color=edge_colors.get(edge_type, 'black'),
                style=edge_styles.get(edge_type, 'solid'),
                connectionstyle=f'arc3,rad={rad}',
                arrows=True,
                arrowstyle='-|>',
                arrowsize=15,
                min_source_margin=15,
                min_target_margin=15,
                width=1.5
            )

    # Create legends for edge types and node types
    edge_legend_elements = [
        Line2D([0], [0], color=edge_colors.get(etype, 'black'), lw=2,
               linestyle=edge_styles.get(etype, 'solid'), label=etype)
        for etype in edge_types
    ]
    node_legend_elements = [
        Line2D([0], [0], marker=node_shapes.get(ntype, 'o'), color='w', label=ntype,
               markerfacecolor=node_colors.get(ntype, 'lightgray'), markersize=15)
        for ntype in node_types
    ]

    # Combine legends
    plt.legend(handles=edge_legend_elements + node_legend_elements, loc='upper left')
    plt.axis('off')
    plt.savefig('plots/go_dependency_graph.png', dpi=300, bbox_inches='tight')
    plt.show()


def traverse_go_directory_structure(graph, root='/'):
    """Traverse and print the directory structure of the Go project"""
    def traverse(node, prefix, is_last):
        if node == root:
            print(f"{node}")
            new_prefix = ''
        else:
            connector = '└── ' if is_last else '├── '
            print(f"{prefix}{connector}{node}")
            new_prefix = prefix + ('    ' if is_last else '│   ')

        # Stop if the current node is a file (leaf node)
        if graph.nodes[node].get('type') == NODE_TYPE_FILE:
            return

        # Traverse neighbors with edge type 'contains'
        neighbors = list(graph.neighbors(node))
        for i, neighbor in enumerate(neighbors):
            for key in graph[node][neighbor]:
                if graph[node][neighbor][key].get('type') == EDGE_TYPE_CONTAINS:
                    is_last_child = (i == len(neighbors) - 1)
                    traverse(neighbor, new_prefix, is_last_child)

    traverse(root, '', False)


def main():
    """Main function to build and analyze Go dependency graph"""
    parser = argparse.ArgumentParser(description='Build dependency graph for Go projects')
    parser.add_argument('--repo_path', type=str, required=True,
                        help='Path to the Go repository root')
    parser.add_argument('--visualize', action='store_true',
                        help='Generate visualization of the dependency graph')
    parser.add_argument('--fuzzy_search', action='store_true', default=True,
                        help='Enable fuzzy search for function matching')
    parser.add_argument('--output_dir', type=str, default='plots',
                        help='Directory to save visualization plots')

    args = parser.parse_args()

    # Ensure output directory exists
    if args.visualize:
        os.makedirs(args.output_dir, exist_ok=True)

    print(f"Building Go dependency graph for: {args.repo_path}")

    try:
        # Build the dependency graph
        graph, _ = build_go_graph(args.repo_path, fuzzy_search=args.fuzzy_search)

        print(f"Graph built successfully!")
        print(f"Total nodes: {graph.number_of_nodes()}")
        print(f"Total edges: {graph.number_of_edges()}")

        # Print statistics
        node_types = []
        edge_types = []

        for node, data in graph.nodes(data=True):
            node_types.append(data.get('type', 'unknown'))

        for _, _, data in graph.edges(data=True):
            edge_types.append(data.get('type', 'unknown'))

        print("\nNode type distribution:")
        node_counter = Counter(node_types)
        for node_type, count in node_counter.items():
            print(f"  {node_type}: {count}")

        print("\nEdge type distribution:")
        edge_counter = Counter(edge_types)
        for edge_type, count in edge_counter.items():
            print(f"  {edge_type}: {count}")

        # Print directory structure
        print("\nProject structure:")
        traverse_go_directory_structure(graph)

        # Generate visualization if requested
        if args.visualize:
            print(f"\nGenerating visualization...")
            visualize_go_graph(graph)
            print(f"Visualization saved to {args.output_dir}/go_dependency_graph.png")

        # Save graph for further analysis (optional)
        import pickle
        with open(f"{args.output_dir}/go_graph.pkl", "wb") as f:
            pickle.dump(graph, f)
        print(f"Graph saved to {args.output_dir}/go_graph.pkl")

    except Exception as e:
        print(f"Error building graph: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == '__main__':
    exit(main())
