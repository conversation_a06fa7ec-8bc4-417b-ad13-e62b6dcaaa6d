# Regularly update the minimum version of astropy-iers-data in pyproject.toml,
# to ensure that if users update astropy, astropy-iers-data will also get
# updated to a recent version.

name: Auto-update astropy-iers-data minimum version

on:
  schedule:
    - cron: '0 0 2 * *'  # Monthly
  workflow_dispatch:

permissions:
  contents: read

jobs:

  update-astropy-iers-data-pin:
    permissions:
      contents: write  # for peter-evans/create-pull-request to create branch
      pull-requests: write  # for peter-evans/create-pull-request to create a PR
    name: Auto-update astropy-iers-data minimum version
    runs-on: ubuntu-latest
    if: github.repository == 'astropy/astropy'
    steps:
    - name: Checkout code
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
      with:
        persist-credentials: false
    - name: Set up Python
      uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065  # v5.6.0
      with:
        python-version: 3.x
    - name: Install dependencies
      run: pip install requests
    - name: Run update script
      run: python .github/workflows/update_astropy_iers_data_pin.py
    - name: Commit changes
      run: |
        git config user.name github-actions
        git config user.email <EMAIL>
        git add pyproject.toml
        if ! git diff --cached --exit-code; then
          git commit -m "Update minimum required version of astropy-iers-data"
        fi
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e  # v7.0.8
      with:
        branch: update-astropy-iers-data-pin
        branch-suffix: timestamp
        delete-branch: true
        labels: no-changelog-entry-needed, utils.iers
        title: Update minimum required version of astropy-iers-data
        body: |
          This is an automated update of the minimum version of astropy-iers-data package.

          :pray: Please apply backport labels to any active backport branches for v6.0.x or later. :pray:

          :warning: Please close and re-open this pull request to trigger the CI. :warning:
