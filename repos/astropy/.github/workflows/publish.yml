name: Wheel building

on:
  schedule:
    # run every day at 4am UTC
    - cron: '0 4 * * *'
  workflow_dispatch:
  push:
  pull_request:
    # We also want this workflow triggered if the 'Build all wheels' label is added
    # or present when PR is updated
    types:
      - synchronize
      - labeled

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_and_publish:
    # This does the actual wheel building and publishing as part of the cron job
    # or if triggered manually via the workflow dispatch, or for a tag.
    permissions:
      contents: none
    uses: OpenAstronomy/github-actions-workflows/.github/workflows/publish.yml@8c0fde6f7e926df6ed7057255d29afa9c1ad5320  # v1.16.0
    if: (github.repository == 'astropy/astropy' && ( startsWith(github.ref, 'refs/tags/v') || github.event_name == 'schedule' || github.event_name == 'workflow_dispatch' || contains(github.event.pull_request.labels.*.name, 'Build all wheels')))
    with:

      # We upload to PyPI for all tags starting with v but not ones ending in .dev
      upload_to_pypi: ${{ startsWith(github.ref, 'refs/tags/v') && !endsWith(github.ref, '.dev') && (github.event_name == 'push') }}
      upload_to_anaconda: ${{ (github.event_name == 'schedule' || github.event_name == 'workflow_dispatch') }}
      anaconda_user: astropy
      anaconda_package: astropy
      anaconda_keep_n_latest: 10

      # For nightly wheels as well as when building with the 'Build all wheels' label, we disable
      # the build isolation and explicitly install the latest developer version of numpy as well as
      # the latest stable versions of all other build-time dependencies.
      env: |
        CIBW_BEFORE_BUILD: '${{ ((github.event_name == 'schedule' || github.event_name == 'workflow_dispatch' || contains(github.event.pull_request.labels.*.name, 'Build all wheels')) && 'pip install -U --pre --extra-index-url https://pypi.anaconda.org/scientific-python-nightly-wheels/simple setuptools>=77.0.0 setuptools_scm cython numpy>=0.0.dev0 extension-helpers') || '' }}'
        CIBW_BUILD_FRONTEND: '${{ ((github.event_name == 'schedule' || github.event_name == 'workflow_dispatch' || contains(github.event.pull_request.labels.*.name, 'Build all wheels')) && 'pip; args: --no-build-isolation') || 'build' }}'

      test_extras: test
      # FIXME: we exclude the test_data_out_of_range test since it
      # currently fails, see https://github.com/astropy/astropy/issues/10409
      # We also exclude test_set_locale as it sometimes relies on the correct locale
      # packages being installed, which it isn't always.
      test_command: pytest -p no:warnings --astropy-header -m "not hypothesis" -k "not test_data_out_of_range and not test_set_locale and not TestQuantityTyping" --pyargs astropy
      targets: |
        # Linux wheels

        - cp311-manylinux_x86_64
        - cp312-manylinux_x86_64
        - cp313-manylinux_x86_64

        - target: cp311-manylinux_aarch64
          runs-on: ubuntu-24.04-arm
        - target: cp312-manylinux_aarch64
          runs-on: ubuntu-24.04-arm
        - target: cp313-manylinux_aarch64
          runs-on: ubuntu-24.04-arm

        # Note that following wheels are not currently tested:
        - cp311-musllinux_x86_64
        - cp312-musllinux_x86_64
        - cp313-musllinux_x86_64

        # MacOS X wheels - as noted in https://github.com/astropy/astropy/pull/12379 we deliberately
        # do not build universal2 wheels.

        - cp311*macosx_x86_64
        - cp312*macosx_x86_64
        - cp313*macosx_x86_64

        - cp311*macosx_arm64
        - cp312*macosx_arm64
        - cp313*macosx_arm64

        # Windows wheels

        - cp311*win32
        - cp312*win32
        - cp313*win32

        - cp311*win_amd64
        - cp312*win_amd64
        - cp313*win_amd64

    secrets:
      pypi_token: ${{ secrets.pypi_token }}
      anaconda_token: ${{ secrets.anaconda_token }}
