name: Check PR change log

on:
  pull_request:
    types: [opened, synchronize, labeled, unlabeled]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  pull-requests: read

jobs:
  changelog_checker:
    name: Check if towncrier change log entry is correct
    runs-on: ubuntu-latest
    steps:
    - uses: scientific-python/action-towncrier-changelog@1d7332022f76e36fe8ce2d716b851f3f98063c62  # v1.0.0
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        BOT_USERNAME: gilesbot
