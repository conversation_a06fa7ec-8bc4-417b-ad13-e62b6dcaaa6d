{"name": "AstroPy", "image": "mcr.microsoft.com/devcontainers/miniconda:0-3", "onCreateCommand": "conda init bash && sudo cp .devcontainer/welcome-message.txt /usr/local/etc/vscode-dev-containers/first-run-notice.txt", "postCreateCommand": "git fetch --tags && pip install tox", "waitFor": "postCreateCommand", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.black-formatter", "charliermarsh.ruff", "stkb.rewrap"]}}}