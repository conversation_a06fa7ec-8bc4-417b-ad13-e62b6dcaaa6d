# %ECSV 1.0
# ---
# delimiter: ','
# datatype:
# -
#   name: i_index
#   datatype: int32
#   description: Row index
# -
#   name: s_byte
#   datatype: int8
# -
#   name: s_short
#   datatype: int16
# -
#   name: s_int
#   datatype: int32
# -
#   name: s_long
#   datatype: int64
# -
#   name: s_float
#   datatype: float32
# -
#   name: s_double
#   datatype: float64
# -
#   name: s_string
#   datatype: string
# -
#   name: s_boolean
#   datatype: bool
# -
#   name: f_byte
#   datatype: string
#   subtype: 'int8[3]'
# -
#   name: f_short
#   datatype: string
#   subtype: 'int16[3]'
# -
#   name: f_int
#   datatype: string
#   subtype: 'int32[3]'
# -
#   name: f_long
#   datatype: string
#   subtype: 'int64[3]'
# -
#   name: f_float
#   datatype: string
#   subtype: 'float32[3]'
# -
#   name: f_double
#   datatype: string
#   subtype: 'float64[3]'
# -
#   name: f_string
#   datatype: string
#   subtype: 'string[3]'
# -
#   name: f_boolean
#   datatype: string
#   subtype: 'bool[3]'
# -
#   name: v_byte
#   datatype: string
#   subtype: 'int8[null]'
# -
#   name: v_short
#   datatype: string
#   subtype: 'int16[null]'
# -
#   name: v_int
#   datatype: string
#   subtype: 'int32[null]'
# -
#   name: v_long
#   datatype: string
#   subtype: 'int64[null]'
# -
#   name: v_float
#   datatype: string
#   subtype: 'float32[null]'
# -
#   name: v_double
#   datatype: string
#   subtype: 'float64[null]'
# -
#   name: v_string
#   datatype: string
#   subtype: 'string[null]'
# -
#   name: v_boolean
#   datatype: string
#   subtype: 'bool[null]'
# -
#   name: m_int
#   datatype: string
#   subtype: 'int32[4,2]'
# -
#   name: m_double
#   datatype: string
#   subtype: 'float64[2,3]'
i_index,s_byte,s_short,s_int,s_long,s_float,s_double,s_string,s_boolean,f_byte,f_short,f_int,f_long,f_float,f_double,f_string,f_boolean,v_byte,v_short,v_int,v_long,v_float,v_double,v_string,v_boolean,m_int,m_double
0,0,0,0,0,0.0,0.0,zero,False,"[0,1,2]","[0,1,2]","[0,1,2]","[0,1,2]","[0.0,null,2.5]","[0.0,null,2.5]","[""foo"",null,""zero""]","[false,false,false]","[0,1,2]","[0,1,2]","[0,1,2]","[0,1,2]","[0.0,null,2.5]","[0.0,null,2.5]","[""foo"",null,""zero""]","[false,false,false]","[[1000,1001],[2000,2001],[3000,3001],[4000,4001]]","[[0.25,0.5,0.75],[-0.25,-0.5,-0.75]]"
1,,1,1,1,1.0,,one,True,,"[1,2,3]","[1,2,3]","[1,2,3]","[1.0,null,3.5]","[1.0,null,3.5]","[""foo"",null,""one""]","[true,false,false]",,"[1,2]","[1,2]","[1,2]","[1.0,null]","[1.0,null]","[""foo"",null]","[true,false]",,"[[1.25,1.5,1.75],[-1.25,-1.5,-1.75]]"
2,2,,2,2,,2.0,two,False,"[2,3,4]",,"[2,3,4]","[2,3,4]","[2.0,null,4.5]","[2.0,null,4.5]","[""foo"",null,""two""]","[false,true,false]",[2],,[2],[2],[2.0],[2.0],"[""foo""]",[false],"[[1002,1003],[2002,2003],[3002,3003],[4002,4003]]",
3,3,3,,3,3.0,3.0,three,True,"[3,4,5]","[3,4,5]",,"[3,4,5]","[3.0,null,5.5]","[3.0,null,5.5]","[""foo"",null,""three""]","[true,true,false]",[],[],,[],[],[],[],[],"[[1003,1004],[2003,2004],[3003,3004],[4003,4004]]","[[3.25,3.5,3.75],[-3.25,-3.5,-3.75]]"
4,4,4,4,,4.0,4.0,four,False,"[4,5,6]","[4,5,6]","[4,5,6]",,"[4.0,null,6.5]","[4.0,null,6.5]","[""foo"",null,""four""]","[false,false,true]","[4,5,6]","[4,5,6]","[4,5,6]",,"[4.0,null,6.5]","[4.0,null,6.5]","[""foo"",null,""four""]","[false,false,true]","[[1004,1005],[2004,2005],[3004,3005],[4004,4005]]","[[4.25,4.5,4.75],[-4.25,-4.5,-4.75]]"
5,5,5,5,5,nan,5.0,five,True,"[5,6,7]","[5,6,7]","[5,6,7]","[5,6,7]",,"[5.0,null,7.5]","[""foo"",null,""five""]","[true,false,true]","[5,6]","[5,6]","[5,6]","[5,6]",,"[5.0,null]","[""foo"",null]","[true,false]","[[1005,1006],[2005,2006],[3005,3006],[4005,4006]]","[[5.25,5.5,5.75],[-5.25,-5.5,-5.75]]"
6,6,6,6,6,6.0,nan,six,False,"[6,7,8]","[6,7,8]","[6,7,8]","[6,7,8]","[6.0,null,8.5]",,"[""foo"",null,""six""]","[false,true,true]",[6],[6],[6],[6],[6.0],,"[""foo""]",[false],"[[1006,1007],[2006,2007],[3006,3007],[4006,4007]]","[[6.25,6.5,6.75],[-6.25,-6.5,-6.75]]"
7,7,7,7,7,7.0,7.0,,True,"[7,8,9]","[7,8,9]","[7,8,9]","[7,8,9]","[7.0,null,9.5]","[7.0,null,9.5]",,"[true,true,true]",[],[],[],[],[],[],,[],"[[1007,1008],[2007,2008],[3007,3008],[4007,4008]]","[[7.25,7.5,7.75],[-7.25,-7.5,-7.75]]"
8,8,8,8,8,8.0,8.0,"' ""\""""' ; '&<>",,"[8,9,10]","[8,9,10]","[8,9,10]","[8,9,10]","[8.0,null,10.5]","[8.0,null,10.5]","[""foo"",null,""' \""\\\""\""' ; '&<>""]",,"[8,9,10]","[8,9,10]","[8,9,10]","[8,9,10]","[8.0,null,10.5]","[8.0,null,10.5]","[""foo"",null,""' \""\\\""\""' ; '&<>""]",,"[[1008,1009],[2008,2009],[3008,3009],[4008,4009]]","[[8.25,8.5,8.75],[-8.25,-8.5,-8.75]]"
9,9,9,9,9,nan,nan,,True,"[9,10,11]","[9,10,11]","[9,10,11]","[9,10,11]","[9.0,null,11.5]","[9.0,null,11.5]","[""foo"",null,""""]","[true,false,false]","[9,10]","[9,10]","[9,10]","[9,10]","[9.0,null]","[9.0,null]","[""foo"",null]","[true,false]","[[1009,1010],[2009,2010],[3009,3010],[4009,4010]]","[[9.25,9.5,9.75],[-9.25,-9.5,-9.75]]"
10,-10,-10,-10,-10,-10.0,-10.0,10,False,"[10,11,12]","[10,11,12]","[10,11,12]","[10,11,12]","[10.0,null,12.5]","[10.0,null,12.5]","[""foo"",null,""10""]","[false,true,false]",[10],[10],[10],[10],[10.0],[10.0],"[""foo""]",[false],"[[1010,1011],[2010,2011],[3010,3011],[4010,4011]]","[[10.25,10.5,10.75],[-10.25,-10.5,-10.75]]"
11,,-11,-11,-11,-11.0,-11.0,10 + one,True,,"[11,12,13]","[11,12,13]","[11,12,13]","[11.0,null,13.5]","[11.0,null,13.5]","[""foo"",null,""10 + one""]","[true,true,false]",,[],[],[],[],[],[],[],,"[[11.25,11.5,11.75],[-11.25,-11.5,-11.75]]"
12,-12,,-12,-12,-12.0,-12.0,10 + two,False,"[12,13,14]",,"[12,13,14]","[12,13,14]","[12.0,null,14.5]","[12.0,null,14.5]","[""foo"",null,""10 + two""]","[false,false,true]","[12,13,14]",,"[12,13,14]","[12,13,14]","[12.0,null,14.5]","[12.0,null,14.5]","[""foo"",null,""10 + two""]","[false,false,true]","[[1012,1013],[2012,2013],[3012,3013],[4012,4013]]",
13,-13,-13,,-13,-13.0,-13.0,10 + three,True,"[13,14,15]","[13,14,15]",,"[13,14,15]","[13.0,null,15.5]","[13.0,null,15.5]","[""foo"",null,""10 + three""]","[true,false,true]","[13,14]","[13,14]",,"[13,14]","[13.0,null]","[13.0,null]","[""foo"",null]","[true,false]","[[1013,1014],[2013,2014],[3013,3014],[4013,4014]]","[[13.25,13.5,13.75],[-13.25,-13.5,-13.75]]"
14,-14,-14,-14,,-14.0,-14.0,10 + four,False,"[14,15,16]","[14,15,16]","[14,15,16]",,"[14.0,null,16.5]","[14.0,null,16.5]","[""foo"",null,""10 + four""]","[false,true,true]",[14],[14],[14],,[14.0],[14.0],"[""foo""]",[false],"[[1014,1015],[2014,2015],[3014,3015],[4014,4015]]","[[14.25,14.5,14.75],[-14.25,-14.5,-14.75]]"
15,-15,-15,-15,-15,nan,-15.0,10 + five,True,"[15,16,17]","[15,16,17]","[15,16,17]","[15,16,17]",,"[15.0,null,17.5]","[""foo"",null,""10 + five""]","[true,true,true]",[],[],[],[],,[],[],[],"[[1015,1016],[2015,2016],[3015,3016],[4015,4016]]","[[15.25,15.5,15.75],[-15.25,-15.5,-15.75]]"
