B/cb        Cataclysmic Binaries, LMXBs, and related objects   (<PERSON>, 2011)
================================================================================
Catalogue of cataclysmic binaries, low-mass X-ray binaries
and related objects (7th Edition, rev. 7.14, September 2010)
     Ritter <PERSON>, Kolb U.
    <Astron. Astrophys. 404, 301 (2003)>
    =2003A&A...404..301R
================================================================================
ADC_Keywords: Binaries, cataclysmic ; Binaries, X-ray ; Novae
Keywords: catalogues - stars: novae, cataclysmic variables -
          stars: binaries: close

Description (Release 7.15):
    Cataclysmic Binaries are semi-detached binaries consisting of a white
    dwarf or a white dwarf precursor primary and a low-mass secondary
    which is filling its critical Roche lobe. The secondary is not
    necessarily unevolved, it may even be a highly evolved star as for
    example in the case of the AM CVn-type stars.

    Low-Mass X-Ray Binaries are semi-detached binaries consisting of
    either a neutron star or a black hole primary, and a low-mass
    secondary which is filling its critical Roche lobe.

    Related Objects are detached binaries consisting of either a white
    dwarf or a white dwarf precursor primary and of a low-mass secondary.
    The secondary may also be a highly evolved star.

    The catalogue lists coordinates, apparent magnitudes, orbital
    parameters, and stellar parameters of the components and other
    characteristic properties of 880 cataclysmic binaries, 98 low-mass
    X-ray binaries and 319 related objects with known or suspected orbital
    periods together with a comprehensive selection of the relevant recent
    literature. In addition the catalogue contains a list of references to
    published finding charts for 1259 of the 1297 objects, and a cross-
    reference list of alias object designations. Literature published
    before 1 July 2010 has, as far as possible, been taken into account.
    Updated information will be provided regularly, currently every six
    months.

    Old editions include catalogue <V/59> (5th edition),
    <V/99> (6th edition) and <V/113> (7th edition);
    the successive versions of the 7th edition are available
    in dedicated subdirectories (v7.00 tp v7.13)

File Summary:
--------------------------------------------------------------------------------
  FileName    Lrecl  Records   Explanations
--------------------------------------------------------------------------------
ReadMe           80        .   This file
cbdata.dat      226      880   Catalogue of Cataclysmic Binaries
lmxbdata.dat    228       98   Catalogue of Low-Mass X-Ray Binaries
pcbdata.dat     216      319   Catalogue of Related Objects
findrefs.dat    274     3230   References for finding charts
cbrefs.dat      257     1937   References for cbdata.dat
lmxbrefs.dat    236      291   References for lmxbdata.dat
pcbrefs.dat     302      655   References for pcbdata.dat
whoswho.txt      72     8927  *Names of objects, and references of designations
whoswho1.dat    199     5052  *Alternative names in lexicographical order
whoswho2.dat    100     3595  *Provisional and Common designations
whoswho5.dat     73     1453  *References to the catalogue acronyms
--------------------------------------------------------------------------------
Note on whoswho.txt:
    contains the 3 parts whoswho1.dat to whoswho5.dat (without the bibcodes)
Note on whoswho1.dat, whoswho2.dat, whoswho5.dat:
    formatted files corresponding to whoswho.txt
--------------------------------------------------------------------------------

See also:
  http://www.MPA-Garching.MPG.DE/RKcat/ : Catalog Home page  or
  http://physics.open.ac.uk/RKcat/      : Catalog Home page

Byte-by-byte Description of file: cbdata.dat
--------------------------------------------------------------------------------
   Bytes Format Units     Label   Explanations
--------------------------------------------------------------------------------
   1- 12  A12   ---       Name    Object name (G1)
      14  A1    ---      whoswho  [*] * indicating that further alternative
                                     designations are in the whoswho1.dat file
  16- 27  A12   ---      AltName  A frequently used alternative name (G2)
  30- 31  I2    h         RAh     Right Ascension J2000 (hours)
  33- 34  I2    min       RAm     Right Ascension J2000 (minutes)
  36- 39  F4.1  s         RAs     [0,60]? Right Ascension J2000 (seconds)
      41  A1    ---       DE-     Declination J2000 (sign)
  42- 43  I2    deg       DEd     Declination J2000 (degrees)
  45- 46  I2    arcmin    DEm     Declination J2000 (minutes of arc)
  48- 49  I2    arcsec    DEs     [0,60]? Declination J2000 (seconds of arc)
      51  A1    arcsec    epos    [0-9] Position accuracy in (G3)
  53- 54  A2    ---       Type1   Object type (3)
      55  A1    ---     u_Type1   [?:] Uncertainty flag for object type
  57- 58  A2    ---       Type2   Object type (3)
      59  A1    ---     u_Type2   [?:] Uncertainty flag for object type
  61- 62  A2    ---       Type3   Object type (3)
      63  A1    ---     u_Type3   [?:] Uncertainty flag for Object type
  65- 66  A2    ---       Type4   Object type (3)
      67  A1    ---     u_Type4   [?] Uncertainty flag for Object type
      69  A1    ---     l_mag1    [><] Limit flag for magnitude mag1
  70- 73  F4.1  mag       mag1    ? Apparent V (or B, b, g, R, I) magnitude
                                    at maximum brightness (4)
      74  A1    ---     f_mag1    [:BbgRiIJKprw] uncertainty flag/band for mag1
                                    (w="white light")
      76  A1    ---     l_mag2    [><] Limit flag for magnitude mag2
  77- 80  F4.1  mag       mag2    ? Apparent V (or B, g, R) magnitude
                                    at mideclipse (5)
      81  A1    ---     f_mag2    [:?BbgRiKpw] uncertainty flag/band for mag2
      83  A1    ---     l_mag3    [><] Limit flag for magnitude mag3
  84- 87  F4.1  mag       mag3    ? Apparent V (or B, g, R) magnitude
                                    of outbursts (6)
      88  A1    ---     f_mag3    [:?BbgpRrw] uncertainty flag/band for mag3
      90  A1    ---     l_mag4    [><] Limit flag for magnitude mag4
  91- 94  F4.1  mag       mag4    ? Apparent V (or B, R) magnitude
                                    in superoutburst (7)
      95  A1    ---     f_mag4    [:?BgRUIpw] uncertainty flag/band for mag4
  97-101  A5    d         T1      Time interval between two subsequent
                                   outbursts (8)
 103-107  A5    d         T2      Time interval between two subsequent
                                   superoutbursts (8)
 109-116  F8.6  d         Orb.Per ? Orbital period, in case of object
                                     type DQ: spectroscopic period, if it is
                                     different from the photometric one
     117  A1    ---     u_Orb.Per [:*] Uncertainty flag for Orb.Per (9)
 119-126  F8.6  d         2.__Per ? Second period (10)
     127  A1    ---     u_2.__Per Uncertainty flag for 2.__Per
 128-137  F10.3 s         3.__Per ? Additional period in the system (11)
     138  A1    ---     f_3.__Per [:TQ] Flag for 3.__Per (12)
 139-148  F10.3 s         4.__Per ? Additional period in the system (13)
     149  A1    ---     f_4.__Per [:T] ":" uncertainty flag for 4.__Per
                                       "T" flag indicating transient pulsations
     151  A1    ---       EB      [D21 ] Flag indicating the
                                          occurrence of eclipses (G4)
     152  A1    ---     u_EB      [?:] Uncertainty flag for EB
     154  I1    ---       SB      [1,2]? Flag specifying the type of
                                          spectroscopic binary (G5)
     155  A1    ---     u_SB      [:] Uncertainty flag for SB
 157-163  A7    ---       SpType2 Spectral type of the secondary (G6)
 165-171  A7    ---       SpType1 Spectral type of the primary (G6)
     174  A1    ---     l_M1/M2   Limit flag for M1/M2
 175-179  F5.2  ---       M1/M2   ? Mass ratio M1/M2
     180  A1    ---     u_M1/M2   Uncertainty flag for M1/M2
 183-186  F4.2  ---     e_M1/M2   ? Error of M1/M2
     188  A1    ---     l_Incl    Limit flag for the orbital inclination
 189-192  F4.1  deg       Incl    ? Orbital inclination
     193  A1    ---     u_Incl    Uncertainty flag for the inclination
 195-198  F4.1  deg     e_Incl    ? Error of orbital inclination
     200  A1    ---     l_M1      Limit flag for primary mass M1
 201-205  F5.3  solMass   M1      ? Primary mass M1
     206  A1    ---     u_M1      Uncertainty flag for primary mass M1
 208-212  F5.3  solMass e_M1      ? Error of primary mass M1
     214  A1    ---     l_M2      Limit flag for secondary mass M2
 215-219  F5.3  solMass   M2      ? Secondary mass M2
     220  A1    ---     u_M2      Uncertainty flag for secondary mass M2
 222-226  F5.3  solMass e_M2      ? Error of secondary mass M2
--------------------------------------------------------------------------------
Note (3): Object type coarsely characterised using the following abbreviations:
    AC = AM CVn star, spectrum devoid of hydrogen lines, subtype of NL
    AM = polar = AM Her system, subtype of NL, contains a synchronously
         or nearly synchronously rotating, magnetized white dwarf
    AS = subtype of AM, with a slowly asynchronously rotating, magnetized
         white dwarf
    BD = secondary star is a brown dwarf
    CP = coherent pulsator, contains a coherently pulsating white dwarf
    CV = cataclysmic variable of unspecified subtype
    DA = non-magnetic direct accretor
    DN = dwarf nova
    DQ = DQ Her star, contains a non-synchronously rotating, magnetized
         white dwarf; usually not seen in X-rays
    EG = extragalactic source
    ER = ER UMa star = SU UMa star with an extremely short supercycle
    GC = source in a globular cluster
    GW = contains a pulsating white dwarf of the GW Vir = PG 1159-035 type
    IP = intermediate polar, shows coherent X-ray period from a
         non-synchronously spinning, magnetized white dwarf; usually a
         strong X-ray source
    LA = low accretion rate polar (LARP), i.e. a somewhat detached magnetic
         CV/pre-CV
    N  = classical nova
    Na = fast nova (decline from max. by 3mag in less than about 100days)
    Nb = slow nova (decline from max. by 3mag in more than about 100days)
    Nc = extremely slow nova (typical time scale of the decline from
         maximum: decades)
    NL = nova-like variable
    Nr = recurrent nova
    NS = system showing negative (nodal) superhumps
    PW = precessing white dwarf
    SH = non-SU UMa star showing either permanent or transient positive
         (apsidal) superhumps
    SS = supersoft X-ray source; CV with stationary hydrogen burning on
         the white dwarf
    SU = SU UMa star, subtype of DN
    SW = SW Sex star, subtype of NL
    UG = dwarf nova of either U Gem or SS Cyg subtype
    UL = ultra-luminous X-ray source
    UX = UX UMa star, subtype of NL
    VY = VY Scl star (anti dwarf nova), subtype of NL
    WZ = WZ Sge star = SU UMa star with an extremely long supercycle
    ZC = Z Cam star, subtype of DN
    ZZ = white dwarf shows ZZ Ceti-type pulsations

Note (4): Apparent V magnitude at maximum brightness of:
    novae (N,Na,Nb,Nc,Nr) in minimum
    DN    (UG,ZC,SU)      in minimum
    NL    (UX,AC)         in normal state
    NL    (DQ,IP,AM,VY)   in high state.
    SS                    in high state.

Note (5): In case of eclipses magnitude at mideclipse, of:
    novae (N,Na,Nb,Nc,Nr) in minimum
    DN    (UG,ZC,SU)      in minimum
    NL    (UX,AC)         in normal state
    NL    (DQ,IP,AM,VY)   in high state.
    SS                    in high state.

Note (6): Apparent magnitude at maximum brightness of:
    novae (N,Na,Nb,Nc,Nr) in outburst
    DN    (UG,ZC)         in outburst
    DN    (SU)            in normal outburst
    DN    (WZ)            in echo outburst
    NL    (AM,VY)         in low state
    NL    (DQ,IP)         in low state
    SS                    in low state.

Note (7): Apparent magnitude at maximum brightness of:
    DN    (ZC)            in standstill
    DN    (SU)            in superoutburst
    WZ                    in superoutburst
    NL    (DQ,IP)         in flaring state or outburst
    iNL    (AM, VY)       in low state
    SS                    in low state

Note (8): Time interval between outbursts is defined:
    - for dwarf novae of subtype UG or ZC: the typical time interval
      between two subsequent outbursts;
    - for dwarf novae of subtype SU:
      T1 is the typical time interval between two subsequent normal
         outburst, and
      T2 is the typical time interval between subsequent superoutbursts.

Note (9): the * indicates, in case of object type SU, that the orbital
    period has been estimated from the known superhump period using the
    empirical relation given by B. Stolz and R. Schoembs (1984A&A...132..187S).

Note (10): The second period is, in case of object type:
     DQ or IP: photometric period if it is different from the
         spectroscopic one
     AM: polarization period = spin period of the white dwarf, if it is
         different from the presumed orbital period (subtype AS)
     SU: superhump period, wherever possible, at the beginning of a
         superoutburst
     SH: photometric period, presumably superhump period of either
         permanent or transient superhumps
     NS: photometric period, period of either permanent or transient
         negative superhumps if 2.__Per. < Orb.Per.

Note (11): This additional period is, in case of object type:
    CP: period of coherent pulsation, (transient if f_3.__Per=T)
    DQ: spin period of the white dwarf
    IP: spin period of the white dwarf, usually detected in X-Rays
    SW: probably the spin period of the white dwarf

Note (12): the flag takes the values:
    ':' uncertainty flag
    'T' indicating transient pulsations
    'Q' indicating the occurrence of quasi- periodic oscillations (QPO)
        in objects of type N, DN, NL.

Note (13): This additional period is, in case of object type:
    CP: second period of coherent pulsation, (transient if f_4.__Per=T)
    DQ: additional period, presumably due to reprocessed X-Rays
    IP: additional period, usually seen in the optical and presumably
        due to reprocessed X-Rays
--------------------------------------------------------------------------------

Byte-by-byte Description of file: lmxbdata.dat
--------------------------------------------------------------------------------
   Bytes Format Units     Label   Explanations
--------------------------------------------------------------------------------
   1- 12  A12   ---       Name    Object name (G1)
      14  A1    ---       whoswho [*] * indicating that further alternative
                                      designations are in the whoswho1.dat file
  16- 27  A12   ---       AltName A frequently used alternative name (G2)
  30- 31  I2    h         RAh     Right Ascension J2000 (hours)
  33- 34  I2    min       RAm     Right Ascension J2000 (minutes)
  36- 39  F4.1  s         RAs     Right Ascension J2000 (seconds)
      41  A1    ---       DE-     Declination J2000 (sign)
  42- 43  I2    deg       DEd     Declination J2000 (degrees)
  45- 46  I2    arcmin    DEm     Declination J2000 (minutes of arc)
  48- 49  I2    arcsec    DEs     Declination J2000 (seconds of arc)
      51  A1    arcsec    epos    [0-9] Position accuracy in (G3)
  53- 54  A2    ---       Type1   Object type (3)
      55  A1    ---     u_Type1   [?] Uncertainty flag for object type
  57- 58  A2    ---       Type2   Object type (3)
      59  A1    ---     u_Type2   [?] Uncertainty flag for object type
  61- 62  A2    ---       Type3   Object type (3)
      63  A1    ---     u_Type3   [?] Uncertainty flag for Object type
  65- 66  A2    ---       Type4   Object type (3)
      67  A1    ---     u_Type4   [?] Uncertainty flag for Object type
      69  A1    ---     l_mag1    [><] Limit flag for magnitude mag1
  70- 73  F4.1  mag       mag1    ? Apparent V (or B, g, R, I, K) magnitude
                                    at maximum brightness,
                                    in case of XT in quiescence
      74  A1    ---     f_mag1    [:UBgRIJK] uncertainty flag/band for mag1
      76  A1    ---     l_mag2    [><] Limit flag for magnitude mag2
  77- 80  F4.1  mag       mag2    ? Apparent V (or B, R, I) magnitude
                                    at mid-eclipse (4)
      81  A1    ---     f_mag2    [:BRIJK] Uncertainty flag/band for mag2
  84- 87  F4.1  mag       mag3    ? Apparent V (or other) magnitude
                                    at outburst (5)
      88  A1    ---     f_mag3    [:BRI] Uncertainty flag/band for mag3
      90  A1    ---     l_mag4    Limit flag of magnitude mag4
  91- 94  F4.1  mag       mag4    ? Apparent V (or other) magnitude at
                                    superoutburst (5)
      96  A1    ---     l_LX/Lopt Limit flag on LX/Lopt
  97-103  F7.1  ---       LX/Lopt ? The ratio of X-ray to optical luminosity
 106-108  I3    d         T1      ? Typical time interval between two subsequent
                                    X-ray active states in case of subtype XT
 110-118  F9.6  d         Orb.Per ? Orbital period
     119  A1    ---     u_Orb.Per [:*] Uncertainty flag for Orb.Per (6)
 120-128  F9.6  d         2.__Per ? Second period, in case of object type SH:
                                    photometric period, presumably superhump
                                    period of either permanent or transient
                                    superhumps
     129  A1    ---     u_2.__Per Uncertainty flag for 2.__Per
 130-140  F11.7 s         3.__Per ? Additional period in the system, in case of
                                    object type
                                    BO: period of burst oscillations = rotation
                                        period of the neutron star;
                                    XP: pulse period of the pulsar
     141  A1    ---     u_3.__Per Uncertainty flag for 3.__Per
 142-146  F5.1  s         4.__Per ? Additional period in the system, in case of
                                    object type XP: optical period, presumably
                                    due to e_processed X-Rays
     152  A1    ---       EB      [D1 ] Occurrence of eclipses (G4)
     153  A1    ---     u_EB      [?] Uncertainty flag on EB
     155  I1    ---       SB      [1,2]? Flag specifying the type of
                                         spectroscopic binary (G5)
 158-164  A7    ---       SpType2 Spectral type of the secondary (G6)
 167-173  A7    ---       SpType1 Spectral type of the primary (G6)
     175  A1    ---     l_M1/M2   Limit flag for M1/M2
 176-180  F5.2  ---       M1/M2   ? Mass ratio M1/M2
     181  A1    ---     u_M1/M2   Uncertainty flag for M1/M2
 182-186  F5.2  ---     e_M1/M2   ? Error of M1/M2
     189  A1    ---     l_Incl    Limit flag for the orbital inclination
 190-193  F4.1  deg       Incl    ? Orbital inclination
     194  A1    ---     u_Incl    Uncertainty flag (:) on Incl
 196-199  F4.1  deg     e_Incl    ? Error of orbital inclination
     201  A1    ---     l_M1      Limit flag on M1
 202-206  F5.2  solMass   M1      ? Primary mass M1
     207  A1    ---     u_M1      Uncertainty flag (:) on M1
 209-213  F5.2  solMass e_M1      ? Error of primary mass M1
     215  A1    ---     l_M2      Limit flag for secondary mass M2
 216-221  F6.3  solMass   M2      ? Secondary mass M2
     222  A1    ---     u_M2      Uncertainty flag (:) on M2
 223-228  F6.3  solMass e_M2      ? Error of secondary mass M2
--------------------------------------------------------------------------------
Note (3): the object type is coarsely characterised using
          the following abbreviations:
    AS = atoll source, subtype of the LMXBs
    BH = black hole candidate, subtype of the LMXBs
    BO = X-ray burster with coherent burst oscillations at the neutron
         star spin period
    DC = source with an accretion disc corona, subtype of the LMXBs
    GC = source in a globular cluster
    MQ = microquasar, source of relativistic jets
    NS = system showing negative (nodal) superhumps
    QN = quiescent neutron star LMXB
    RP = primary is also seen as a radio pulsar
    SH = system showing either permanent or transient superhumps
    SS = supersoft X-ray source
    UL = ultra-luminous X-ray source
    XB = X-ray burst source
    XP = X-ray pulsar
    XT = transient X-ray source
    ZS = Z-source, subtype of the LMXBs

Note (4): in case of eclipses magnitude at mideclipse,
          in case of XT in quiescence

Note (5): in case of XL (XB, XT) in outburst

Note (6): the * indicates, in case of object type SU, that the orbital
    period has been estimated from the known superhump period using the
    empirical relation given by B. Stolz and R. Schoembs (1984A&A...132..187S).
--------------------------------------------------------------------------------

Byte-by-byte Description of file: pcbdata.dat
--------------------------------------------------------------------------------
   Bytes Format Units    Label    Explanations
--------------------------------------------------------------------------------
   1- 12  A12   ---       Name    Object name (G1)
      14  A1    ---      whoswho  [*] * indicating that further alternative
                                    designations are in the whoswho1.dat file
  16- 27  A12   ---      AltName  A frequently used alternative name (G2)
  30- 31  I2    h         RAh     ? Right Ascension J2000 (hours)
  33- 34  I2    min       RAm     ? Right Ascension J2000 (minutes)
  36- 39  F4.1  s         RAs     ? Right Ascension J2000 (seconds)
      41  A1    ---       DE-     ? Declination J2000 (sign)
  42- 43  I2    deg       DEd     ? Declination J2000 (degrees)
  45- 46  I2    arcmin    DEm     ? Declination J2000 (minutes of arc)
  48- 49  I2    arcsec    DEs     ? Declination J2000 (seconds of arc)
      51  A1    arcsec    epos    [0-9P] Position accuracy (G3)
  53- 54  A2    ---      Type1    Object type (3)
      55  A1    ---    u_Type1    [?] Uncertainty flag for object type
  57- 58  A2    ---      Type2    Object type (3)
      59  A1    ---    u_Type2    [?] Uncertainty flag for object type
  61- 62  A2    ---      Type3    Object type (3)
      63  A1    ---    u_Type3    [?] Uncertainty flag for object type
  65- 66  A2    ---      Type4    Object type (3)
  70- 73  F4.1  mag       mag1    ? Apparent V (or other) magnitude at maximum
                                    brightness outside eclipse
      74  A1    ---     f_mag1    [:BbpgRiIK] uncertainty flag/band for mag1
      76  A1    ---     l_mag2    [><] Limit flag for magnitude mag2
  77- 80  F4.1  mag       mag2    ? Apparent V (or other) magnitude at minimum
                                    brightness, in case of eclipses magnitude
                                    at mideclipse.
      81  A1    ---     f_mag2    [:BgRiI] uncertainty flag/band for mag2
  82- 90  F9.6  d         Orb.Per Orbital period
      91  A1    ---     u_Orb.Per Uncertainty flag for Orb.Per
  92-101  F10.4 s         2.__Per ? Spin period of the accretor (white dwarf
                                    or neutron star).
     103  I1    ---       EB      [1,2]? Flag indicating the occurrence of
                                        eclipses (G4)
     104  A1    ---     u_EB      [?] Uncertainty flag for EB
     106  I1    ---       SB      [1,2]? Flag specifying the type of
                                         spectroscopic binary (G5)
 109-115  A7    ---      SpType2  Spectral type of the secondary (G6)
 117-123  A7    ---      SpType1  Spectral type of the primary (G6)
     125  A1    ---     l_E       [><] Limit flag for the orbital eccentricity
 126-129  F4.2  ---       E       ? Orbital eccentricity
     130  A1    ---     u_E       Uncertainty flag on orbital eccentricity
 132-135  F4.2  ---     e_E       ? Error of orbital eccentricity
     137  A1    ---     l_M1/M2   Limit flag for mass ratio M1/M2
 138-141  F4.2  ---       M1/M2   ? Mass ratio M1/M2
     142  A1    ---     u_M1/M2   Uncertainty flag on mass ratio M1/M2
 144-147  F4.2  ---     e_M1/M2   ? Error of M1/M2
     149  A1    ---     l_Incl    Limit flag for the orbital inclination
 150-153  F4.1  deg       Incl    ? Orbital inclination
     154  A1    ---     u_Incl    Uncertainty flag for the inclination
 156-159  F4.1  deg     e_Incl    ? Error of orbital inclination
     161  A1    ---     l_M1      Limit flag for primary mass M1
 162-166  F5.3  solMass   M1      ? Primary mass M1
     167  A1    ---     u_M1      Uncertainty flag for primary mass M1
 169-173  F5.3  solMass e_M1      ? Error of primary mass M1
     175  A1    ---     l_R1      Limit flag for primary radius R1
 176-180  F5.3  solRad    R1      ? Primary radius
     181  A1    ---     u_R1      Uncertainty flag [:] for primary radius R1
 183-187  F5.3  solRad  e_R1      ? Error of primary radius R1
     189  A1    ---     l_M2      Limit flag for secondary mass M2
 190-194  F5.3  solMass   M2      ? Secondary mass M2
     195  A1    ---     u_M2      Uncertainty flag for secondary mass M2
 197-201  F5.3  solMass e_M2      ? Error of secondary mass M2
     203  A1    ---     l_R2      Limit flag on secondary radius R2
 204-209  F6.4  solRad    R2      ? Secondary radius R2
     210  A1    ---     u_R2      Uncertainty flag [:] for secondary radius R2
 212-217  F6.4  solRad  e_R2      ? Error of secondary radius
--------------------------------------------------------------------------------
Note (3): Object type coarsely characterised using the following abbreviations:
     CP = coherent pulsator, contains a coherently pulsating white dwarf or
          subdwarf
     DD = system consists of two degenerate components
     DS = detached system
     EC = contains a pulsating sdB star of the EC 14026-2647 type
     GC = source in a globular cluster
     GP = sdB-star with g-mode pulsations
     GW = contains a pulsating white dwarf of the GW Vir = PG 1159-035 type
     PN = central star of a planetary nebula
     RS = system shows RS CVn-like chromospheric activity
     SC = sub-stellar companion
--------------------------------------------------------------------------------

Byte-by-byte Description of file: *refs.dat
--------------------------------------------------------------------------------
   Bytes Format Units   Label     Explanations
--------------------------------------------------------------------------------
   1- 12  A12   ---     Name      Object name
  14- 32  A19   ---     BibCode   BibCode
  34-302  A269  ---     Text      Text of reference
--------------------------------------------------------------------------------

Byte-by-byte Description of file: whoswho1.dat
--------------------------------------------------------------------------------
   Bytes Format Units   Label     Explanations
--------------------------------------------------------------------------------
       1  <USER>    <GROUP>     B         [B] when the name is based on B1950 position
   2- 25  A24   ---     Name      Object name
      27  A1    ---     ---       [=]
  29-212  A184  ---     AltName   Other name, or comment (1)
--------------------------------------------------------------------------------
Note (1): Catalogue designations involving the equatorial coordinates
          are given in the following format:
   HHMM+DDMM (catalogue acronyms) if the position is given in B1950
              coordinates -- a 'B' is then present in byte 1.
  JHHMM+DDMM (catalogue acronyms) if the position is given in J2000
              coordinates.
         Here HHMM is the truncated right ascension in hours (HH) and
              minutes (MM), DDMM the truncated declination in  degrees (DD)
              and arcminutes (MM), and + the sign of the declination.
--------------------------------------------------------------------------------

Byte-by-byte Description of file: whoswho2.dat
--------------------------------------------------------------------------------
   Bytes Format Units   Label     Explanations
--------------------------------------------------------------------------------
       1  <USER>    <GROUP>     B         [B] when the name is based on B1950 position
   2- 53  A52   ---     cName     Common or Provisional designation (G2)
  55- 56  A2    ---     ---       [->]
  58-101  A44   ---     Name      Usual name
--------------------------------------------------------------------------------

Byte-by-byte Description of file: whoswho5.dat
--------------------------------------------------------------------------------
   Bytes Format Units   Label     Explanations
--------------------------------------------------------------------------------
   1- 10  A10   ---     Abbr      Catalogue abbreviation
  14- 73  A60   ---     Text      Text of References
--------------------------------------------------------------------------------

Global Notes:

Note (G1): Wherever possible, the designation of the object given in the
    General Catalogue of Variable Stars (Cat. <II/214>) is used here.

Note (G2): The acronyms used in lists are detailed in the last part of
    the file "whoswho.txt"

Note (G3): The number indicates the accuracy of position in seconds of arc.
     If the positional error is larger than 9arcsec, or unknown, this field
     is left blank. The letter [P] indicates an object with a large proper
     motion.

Note (G4): The EB flag means:
    EB= : (blank) no eclipses observed.
    EB=1: 1 eclipse per orbital revolution observed.
    EB=2: 2 eclipses per orbital revolution observed.
    EB=D: periodic eclipse-like dips observed.

Note (G5): The SB flag means:
    SB=1: single-line spectroscopic binary
    SB=2: double-line spectroscopic binary

Note (G6): Spectral types are given in the following format:
    [Spectral class/Luminosity class], where the usual roman numerals for
    the latter are replaced by the corresponding arabic numerals, i.e.
    I = 1, II = 2, III = 3, IV = 4, V = 5, VI = 6.
--------------------------------------------------------------------------------

History:
  * 16-Apr-2003: 7th Edition
  * 28-Aug-2003: 7.1 Edition
  * 12-Mar-2004: 7.2 Edition
  * 01-Sep-2004: 7.3 Edition
  * 24-Mar-2005: 7.4 Edition
  * 25-Jul-2005: 7.5 Edition
  * 01-Feb-2006: 7.6 Edition
  * 29-May-2006: 7.6rev1 Edition (no new object)
  * 07-Dec-2006: 7.7 Edition
  * 17-Aug-2007: 7.8 Edition
  * 18-Mar-2008: 7.9 Edition
  * 26-Jul-2008: 7.10 Edition
  * 06-Apr-2009: 7.11 Edition
  * 18-Sep-2009: 7.12 Edition
  * 20-Mar-2010: 7.13 Edition
  * 05-Nov-2010: 7.14 Edition
  * 23-Mar-2011: 7.15 Edition

References:
  Ritter H., 1984A&AS...57..385R (3rd edition)
  Ritter H., 1987A&AS...70..335R (4th edition)
  Ritter H., 1990A&AS...85.1179R (5th edition) (Catalogue: V/59)
  Ritter H., Kolb U., 1995, in "X-ray Binaries", Lewin W.H.G,
    van Paradijs J., van den Heuvel E.P. (eds),
    Cambridge Univ. Press, p. 578 (Cat. <V/82>)
================================================================================
(End) H. Ritter, U. Kolb [MPA Garching], Francois Ochsenbein [CDS]   05-Nov-2010
