# Licensed under a 3-clause BSD style license - see LICENSE.rst

# Quantization dithering method constants; these are right out of fitsio.h
NO_DITHER = -1
SUBTRACTIVE_DITHER_1 = 1
SUBTRACTIVE_DITHER_2 = 2
QUANTIZE_METHOD_NAMES = {
    NO_DITHER: "NO_DITHER",
    SUB<PERSON>ACTIVE_DITHER_1: "SUBTRACTIVE_DITHER_1",
    SUBTRACTIVE_DITHER_2: "SUBTRACTIVE_DITHER_2",
}
DITHER_SEED_CLOCK = 0
DITHER_SEED_CHECKSUM = -1

COMPRESSION_TYPES = (
    "NOCOMPRESS",
    "RICE_1",
    "GZIP_1",
    "GZIP_2",
    "PLIO_1",
    "HCOMPRESS_1",
)

# Default compression parameter values
DEFAULT_COMPRESSION_TYPE = "RICE_1"
DEFAULT_QUANTIZE_LEVEL = 16.0
DEFAULT_QUANTIZE_METHOD = NO_DITHER
DEFAULT_DITHER_SEED = DITHER_SEED_CLOCK
DEFAULT_HCOMP_SCALE = 0
DEFAULT_HCOMP_SMOOTH = 0
DEFAULT_BLOCK_SIZE = 32
DEFAULT_BYTE_PIX = 4

CMTYPE_ALIASES = {"RICE_ONE": "RICE_1"}

COMPRESSION_KEYWORDS = {
    "ZIMAGE",
    "ZCMPTYPE",
    "ZBITPIX",
    "ZNAXIS",
    "ZMASKCMP",
    "ZSIMPLE",
    "ZTENSION",
    "ZEXTEND",
}
