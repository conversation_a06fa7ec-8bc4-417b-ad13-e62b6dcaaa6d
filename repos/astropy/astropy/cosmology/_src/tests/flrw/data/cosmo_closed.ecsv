# %ECSV 1.0
# ---
# datatype:
# - {name: redshift, unit: redshift, datatype: float64}
# - {name: dm, unit: Mpc, datatype: float64}
# - {name: da, unit: Mpc, datatype: float64}
# - {name: dl, unit: Mpc, datatype: float64}
# meta: !!omap
# - {source: icosmo (icosmo.org)}
# - {Om: 2}
# - {w: -1}
# - {h: 0.7}
# - {Ol: 0.1}
# - __serialized_columns__:
#     da:
#       __class__: astropy.units.quantity.Quantity
#       unit: &id001 !astropy.units.Unit {unit: Mpc}
#       value: !astropy.table.SerializedColumn {name: da}
#     dl:
#       __class__: astropy.units.quantity.Quantity
#       unit: *id001
#       value: !astropy.table.SerializedColumn {name: dl}
#     dm:
#       __class__: astropy.units.quantity.Quantity
#       unit: *id001
#       value: !astropy.table.SerializedColumn {name: dm}
#     redshift:
#       __class__: astropy.units.quantity.Quantity
#       unit: !astropy.units.Unit {unit: redshift}
#       value: !astropy.table.SerializedColumn {name: redshift}
# schema: astropy-2.0
redshift dm da dl
0.0 0.0 0.0 0.0
0.1625 601.8016 517.67879 699.59436
0.325 1057.9502 798.45297 1401.784
0.5 1438.2161 958.81076 2157.3242
0.6625 1718.6778 1033.7912 2857.3019
0.825 1948.24 1067.5288 3555.5381
1.0 2152.7954 1076.3977 4305.5908
1.1625 2312.3427 1069.2914 5000.441
1.325 2448.9755 1053.3228 5693.8681
1.5 2575.6795 1030.2718 6439.1988
1.6625 2677.9671 1005.8092 7130.0873
1.825 2768.1157 979.86398 7819.927
2.0 2853.9222 951.30739 8561.7665
2.1625 2924.8116 924.84161 9249.7167
2.325 2988.5333 898.80701 9936.8732
2.5 3050.3065 871.51614 10676.073
2.6625 3102.1909 847.01459 11361.774
2.825 3149.5043 823.39982 12046.854
3.0 3195.9966 798.99915 12783.986
3.1625 3235.5334 777.30533 13467.908
3.325 3271.9832 756.5279 14151.327
3.5 3308.1758 735.15017 14886.791
3.6625 3339.2521 716.19347 15569.263
3.825 3368.1489 698.06195 16251.319
4.0 3397.0803 679.41605 16985.401
4.1625 3422.1142 662.87926 17666.664
4.325 3445.5542 647.05243 18347.576
4.5 3469.1805 630.76008 19080.493
4.6625 3489.7534 616.29199 19760.729
