# %ECSV 1.0
# ---
# datatype:
# - {name: redshift, unit: redshift, datatype: float64}
# - {name: dm, unit: Mpc, datatype: float64}
# - {name: da, unit: Mpc, datatype: float64}
# - {name: dl, unit: Mpc, datatype: float64}
# meta: !!omap
# - {source: icosmo (icosmo.org)}
# - {Om: 0.3}
# - {w: -1}
# - {h: 0.7}
# - {Ol: 0.7}
# - __serialized_columns__:
#     da:
#       __class__: astropy.units.quantity.Quantity
#       unit: &id001 !astropy.units.Unit {unit: Mpc}
#       value: !astropy.table.SerializedColumn {name: da}
#     dl:
#       __class__: astropy.units.quantity.Quantity
#       unit: *id001
#       value: !astropy.table.SerializedColumn {name: dl}
#     dm:
#       __class__: astropy.units.quantity.Quantity
#       unit: *id001
#       value: !astropy.table.SerializedColumn {name: dm}
#     redshift:
#       __class__: astropy.units.quantity.Quantity
#       unit: !astropy.units.Unit {unit: redshift}
#       value: !astropy.table.SerializedColumn {name: redshift}
# schema: astropy-2.0
redshift dm da dl
0.0 0.0 0.0 0.0
0.1625 669.77536 576.15085 778.61386
0.325 1285.5964 970.26143 1703.4152
0.5 1888.6254 1259.0836 2832.9381
0.6625 2395.5489 1440.9317 3982.6
0.825 2855.5732 1564.6976 5211.421
1.0 3303.8288 1651.9144 6607.6577
1.1625 3681.1867 1702.2829 7960.5663
1.325 4025.5229 1731.4077 9359.3408
1.5 4363.8558 1745.5423 10909.64
1.6625 4651.483 1747.0359 12384.573
1.825 4916.597 1740.3883 13889.387
2.0 5179.8621 1726.6207 15539.586
2.1625 5406.0204 1709.4136 17096.54
2.325 5616.5075 1689.1752 18674.888
2.5 5827.5418 1665.012 20396.396
2.6625 6010.4886 1641.089 22013.414
2.825 6182.1688 1616.2533 23646.796
3.0 6355.6855 1588.9214 25422.742
3.1625 6507.2491 1563.3031 27086.425
3.325 6650.452 1537.6768 28763.205
3.5 6796.1499 1510.2555 30582.674
3.6625 6924.2096 1485.0852 32284.127
3.825 7045.8876 1460.2876 33996.408
4.0 7170.3664 1434.0733 35851.832
4.1625 7280.3423 1410.2358 37584.767
4.325 7385.3277 1386.916 39326.87
4.5 7493.2222 1362.404 41212.722
4.6625 7588.9589 1340.2135 42972.48
