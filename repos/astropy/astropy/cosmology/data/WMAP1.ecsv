# %ECSV 1.0
# ---
# datatype:
# - {name: name, datatype: string}
# - {name: H0, unit: km / (Mpc s), datatype: float64, format: '', description: Hubble constant as an `~astropy.units.Quantity` at z=0.}
# - {name: Om0, datatype: float64, format: '', description: Omega matter; matter density/critical density at z=0.}
# - {name: Tcmb0, unit: K, datatype: float64, format: '', description: Temperature of the CMB as `~astropy.units.Quantity` at z=0.}
# - {name: Neff, datatype: float64, format: '', description: Number of effective neutrino species.}
# - {name: m_nu, unit: eV, datatype: string, format: '', description: Mass of neutrino species., subtype: 'float64[3]'}
# - {name: Ob0, datatype: float64, format: '', description: Omega baryon; baryonic matter density/critical density at z=0.}
# meta: !!omap
# - {Oc0: 0.213}
# - {n: 0.96}
# - {sigma8: 0.75}
# - {tau: 0.117}
# - z_reion: !astropy.units.Quantity
#     unit: !astropy.units.Unit {unit: redshift}
#     value: 17.0
# - t0: !astropy.units.Quantity
#     unit: !astropy.units.Unit {unit: Gyr}
#     value: 13.4
# - {reference: 'Spergel et al. 2003, ApJS, 148, 175, doi:  10.1086/377226. Table 7 (WMAP + CBI + ACBAR + 2dFGRS + Lya).\nPending WMAP
#     team approval and subject to change.'}
# - {cosmology: FlatLambdaCDM}
# - __serialized_columns__:
#     H0:
#       __class__: astropy.units.quantity.Quantity
#       unit: !astropy.units.Unit {unit: km / (Mpc s)}
#       value: !astropy.table.SerializedColumn {name: H0}
#     Tcmb0:
#       __class__: astropy.units.quantity.Quantity
#       unit: !astropy.units.Unit {unit: K}
#       value: !astropy.table.SerializedColumn {name: Tcmb0}
#     m_nu:
#       __class__: astropy.units.quantity.Quantity
#       unit: !astropy.units.Unit {unit: eV}
#       value: !astropy.table.SerializedColumn {name: m_nu}
# schema: astropy-2.0
name H0 Om0 Tcmb0 Neff m_nu Ob0
WMAP1 72.0 0.257 2.725 3.04 [0.0,0.0,0.0] 0.0436
