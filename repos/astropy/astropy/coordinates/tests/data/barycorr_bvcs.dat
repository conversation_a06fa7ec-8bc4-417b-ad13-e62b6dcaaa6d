# def _get_barycorr_bvcs(coos, loc, injupyter=False):
#     """
#     Gets the barycentric correction of the test data from the
#     http://astroutils.astronomy.ohio-state.edu/exofast/barycorr.html web site.
#     Requires the https://github.com/tronsgaard/barycorr python interface to that
#     site.
#
#     Provided to reproduce the test data below.
#
#     """
#     import barycorr
#
#     from astropy.utils.console import ProgressBar
#
#     bvcs = []
#     for ra, dec in ProgressBar(
#         list(zip(coos.ra.deg, coos.dec.deg)), ipython_widget=injupyter
#     ):
#         res = barycorr.bvc(
#             test_input_time.utc.jd,  # Global variable in test_velocity_corrs.py
#             ra,
#             dec,
#             lat=loc.geodetic[1].deg,
#             lon=loc.geodetic[0].deg,
#             elevation=loc.geodetic[2].to(u.m).value,
#         )
#         bvcs.append(res)
#     return bvcs * u.m / u.s
-10335.93326096
-14198.47605491
-2237.60012494
-14198.47595363
-17425.46512587
-17131.70901174
2424.37095076
2130.61519166
-17425.46495779
-19872.50026998
-24442.37091097
-11017.08975893
6978.0622355
11547.93333743
-1877.34772637
-19872.50004258
-21430.08240017
-27669.14280689
-16917.08506807
2729.57222968
16476.49569232
13971.97171764
-2898.04250914
-21430.08212368
-22028.51337105
-29301.92349394
-21481.13036199
-3147.44828909
14959.50065514
22232.91155425
14412.11903105
-3921.56359768
-22028.51305781
-21641.01479409
-29373.0512649
-24205.90521765
-8557.34138828
10250.50350732
23417.2299926
24781.98057941
13706.17339044
-4627.70005932
-21641.01445812
-20284.92627505
-28193.91696959
-22908.51624166
-6901.82132125
12336.45758056
25804.51614607
27200.50029664
15871.21385688
-2882.24738355
-20284.9259314
-18020.92947805
-25752.96564978
-20585.81957567
-4937.25573801
13870.58916957
27037.31568441
28402.06636994
17326.25977035
-1007.62209045
-18020.92914212
-14950.33284575
-22223.74260839
-14402.94943965
3930.73265119
22037.68163353
29311.09265126
21490.30070307
3156.62229843
-14950.33253252
-11210.53846867
-17449.59867676
-6697.54090389
12949.11642965
26696.03999586
24191.5164355
7321.50355488
-11210.53819218
-6968.89359681
-11538.76423011
1886.51695238
19881.66902396
24451.54039956
11026.26000765
-6968.89336945
-2415.20201758
-2121.44599781
17434.63406085
17140.87871753
-2415.2018495
2246.76923076
14207.64513054
2246.76933194
6808.40787728
