# Licensed under a 3-clause BSD style license - see LICENSE.rst

# This file was automatically generated from ply. To re-generate this file,
# remove it from this folder, then build astropy and run the tests in-place:
#
#   python setup.py build_ext --inplace
#   pytest astropy/coordinates/angles
#
# You can then commit the changes to this file.


# angle_parsetab.py
# This file is automatically generated. Do not edit.
# pylint: disable=W,C,R
_tabversion = '3.10'

_lr_method = 'LALR'

_lr_signature = 'COLON DEGREE EASTWEST HOUR MINUTE NORTHSOUTH SECOND SIGN SIMPLE_UNIT UFLOAT UINT\n            angle : sign hms eastwest\n                  | sign dms dir\n                  | sign simple dir\n            \n            sign : SIGN\n                 |\n            \n            eastwest : EASTWEST\n                     |\n            \n            dir : EASTWEST\n                | NORTHSOUTH\n                |\n            \n            ufloat : UFLOAT\n                   | UINT\n            \n            generic : ufloat\n                    | UINT ufloat\n                    | UINT COLON ufloat\n                    | UINT UINT ufloat\n                    | UINT COLON UINT COLON ufloat\n            \n            hms : UINT HOUR\n                | UINT HOUR ufloat\n                | UINT HOUR UINT MINUTE\n                | UINT HOUR UFLOAT MINUTE\n                | UINT HOUR UINT MINUTE ufloat\n                | UINT HOUR UINT MINUTE ufloat SECOND\n                | generic HOUR\n            \n            dms : UINT DEGREE\n                | UINT DEGREE ufloat\n                | UINT DEGREE UINT MINUTE\n                | UINT DEGREE UFLOAT MINUTE\n                | UINT DEGREE UINT MINUTE ufloat\n                | UINT DEGREE UINT MINUTE ufloat SECOND\n                | generic DEGREE\n            \n            simple : generic\n                   | generic MINUTE\n                   | generic SECOND\n                   | generic SIMPLE_UNIT\n            '
    
_lr_action_items = {'SIGN':([0,],[3,]),'UINT':([0,2,3,7,17,18,20,21,37,39,41,],[-5,7,-4,17,27,29,32,35,27,27,27,]),'UFLOAT':([0,2,3,7,17,18,20,21,37,39,41,],[-5,9,-4,9,9,31,34,9,9,9,9,]),'$end':([1,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,43,44,45,46,],[0,-7,-10,-10,-12,-13,-11,-32,-1,-6,-2,-8,-9,-3,-12,-18,-14,-25,-24,-31,-33,-34,-35,-12,-16,-12,-19,-11,-12,-26,-11,-12,-15,-20,-21,-27,-28,-22,-29,-17,-23,-30,]),'EASTWEST':([4,5,6,7,8,9,10,17,18,19,20,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,43,44,45,46,],[12,14,14,-12,-13,-11,-32,-12,-18,-14,-25,-24,-31,-33,-34,-35,-12,-16,-12,-19,-11,-12,-26,-11,-12,-15,-20,-21,-27,-28,-22,-29,-17,-23,-30,]),'NORTHSOUTH':([5,6,7,8,9,10,17,19,20,23,24,25,26,27,28,32,33,34,35,36,39,40,43,44,46,],[15,15,-12,-13,-11,-32,-12,-14,-25,-31,-33,-34,-35,-12,-16,-12,-26,-11,-12,-15,-27,-28,-29,-17,-30,]),'HOUR':([7,8,9,10,17,19,27,28,35,36,44,],[18,-13,-11,22,-12,-14,-12,-16,-12,-15,-17,]),'DEGREE':([7,8,9,10,17,19,27,28,35,36,44,],[20,-13,-11,23,-12,-14,-12,-16,-12,-15,-17,]),'COLON':([7,35,],[21,41,]),'MINUTE':([7,8,9,10,17,19,27,28,29,31,32,34,35,36,44,],[-12,-13,-11,24,-12,-14,-12,-16,37,38,39,40,-12,-15,-17,]),'SECOND':([7,8,9,10,17,19,27,28,35,36,42,43,44,],[-12,-13,-11,25,-12,-14,-12,-16,-12,-15,45,46,-17,]),'SIMPLE_UNIT':([7,8,9,10,17,19,27,28,35,36,44,],[-12,-13,-11,26,-12,-14,-12,-16,-12,-15,-17,]),}

_lr_action = {}
for _k, _v in _lr_action_items.items():
   for _x,_y in zip(_v[0],_v[1]):
      if not _x in _lr_action:  _lr_action[_x] = {}
      _lr_action[_x][_k] = _y
del _lr_action_items

_lr_goto_items = {'angle':([0,],[1,]),'sign':([0,],[2,]),'hms':([2,],[4,]),'dms':([2,],[5,]),'simple':([2,],[6,]),'ufloat':([2,7,17,18,20,21,37,39,41,],[8,19,28,30,33,36,42,43,44,]),'generic':([2,],[10,]),'eastwest':([4,],[11,]),'dir':([5,6,],[13,16,]),}

_lr_goto = {}
for _k, _v in _lr_goto_items.items():
   for _x, _y in zip(_v[0], _v[1]):
       if not _x in _lr_goto: _lr_goto[_x] = {}
       _lr_goto[_x][_k] = _y
del _lr_goto_items
_lr_productions = [
  ("S' -> angle","S'",1,None,None,None),
  ('angle -> sign hms eastwest','angle',3,'p_angle','formats.py',170),
  ('angle -> sign dms dir','angle',3,'p_angle','formats.py',171),
  ('angle -> sign simple dir','angle',3,'p_angle','formats.py',172),
  ('sign -> SIGN','sign',1,'p_sign','formats.py',183),
  ('sign -> <empty>','sign',0,'p_sign','formats.py',184),
  ('eastwest -> EASTWEST','eastwest',1,'p_eastwest','formats.py',190),
  ('eastwest -> <empty>','eastwest',0,'p_eastwest','formats.py',191),
  ('dir -> EASTWEST','dir',1,'p_dir','formats.py',197),
  ('dir -> NORTHSOUTH','dir',1,'p_dir','formats.py',198),
  ('dir -> <empty>','dir',0,'p_dir','formats.py',199),
  ('ufloat -> UFLOAT','ufloat',1,'p_ufloat','formats.py',205),
  ('ufloat -> UINT','ufloat',1,'p_ufloat','formats.py',206),
  ('generic -> ufloat','generic',1,'p_generic','formats.py',212),
  ('generic -> UINT ufloat','generic',2,'p_generic','formats.py',213),
  ('generic -> UINT COLON ufloat','generic',3,'p_generic','formats.py',214),
  ('generic -> UINT UINT ufloat','generic',3,'p_generic','formats.py',215),
  ('generic -> UINT COLON UINT COLON ufloat','generic',5,'p_generic','formats.py',216),
  ('hms -> UINT HOUR','hms',2,'p_hms','formats.py',228),
  ('hms -> UINT HOUR ufloat','hms',3,'p_hms','formats.py',229),
  ('hms -> UINT HOUR UINT MINUTE','hms',4,'p_hms','formats.py',230),
  ('hms -> UINT HOUR UFLOAT MINUTE','hms',4,'p_hms','formats.py',231),
  ('hms -> UINT HOUR UINT MINUTE ufloat','hms',5,'p_hms','formats.py',232),
  ('hms -> UINT HOUR UINT MINUTE ufloat SECOND','hms',6,'p_hms','formats.py',233),
  ('hms -> generic HOUR','hms',2,'p_hms','formats.py',234),
  ('dms -> UINT DEGREE','dms',2,'p_dms','formats.py',245),
  ('dms -> UINT DEGREE ufloat','dms',3,'p_dms','formats.py',246),
  ('dms -> UINT DEGREE UINT MINUTE','dms',4,'p_dms','formats.py',247),
  ('dms -> UINT DEGREE UFLOAT MINUTE','dms',4,'p_dms','formats.py',248),
  ('dms -> UINT DEGREE UINT MINUTE ufloat','dms',5,'p_dms','formats.py',249),
  ('dms -> UINT DEGREE UINT MINUTE ufloat SECOND','dms',6,'p_dms','formats.py',250),
  ('dms -> generic DEGREE','dms',2,'p_dms','formats.py',251),
  ('simple -> generic','simple',1,'p_simple','formats.py',262),
  ('simple -> generic MINUTE','simple',2,'p_simple','formats.py',263),
  ('simple -> generic SECOND','simple',2,'p_simple','formats.py',264),
  ('simple -> generic SIMPLE_UNIT','simple',2,'p_simple','formats.py',265),
]
