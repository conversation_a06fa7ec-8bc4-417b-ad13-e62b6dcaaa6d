# Licensed under a 3-clause BSD style license - see LICENSE.rst

"""
This subpackage contains classes and functions for celestial coordinates
of astronomical objects. It also contains a framework for conversions
between coordinate systems.
"""

from .angles import *
from .attributes import *
from .baseframe import *
from .builtin_frames import *
from .calculation import *
from .distances import *
from .earth import *
from .errors import *
from .funcs import *
from .matching import *
from .name_resolve import *
from .polarization import *
from .representation import *
from .sky_coordinate import *
from .solar_system import *
from .spectral_coordinate import *
from .spectral_quantity import *
from .transformations import *
