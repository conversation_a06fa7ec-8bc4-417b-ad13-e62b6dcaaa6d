{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_001", "base_commit": "b1bc3a6372c624606af4eb214f7718ec2c0ca06f", "patch": "", "problem_statement": "我需要理解如何将ps查询数据转成chatbot后端支持的echart图形数据格式 特别是饼图和折线图的差异", "edit_functions": ["mmvsops-agent/src/agents/agent_prometheus/utils/graph_util.py.transform_graph"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_031", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "帮我写一个用例用于验证磁盘IOPS算法结果获取，要求验证巡检结果是否准确", "edit_functions": ["mmvsops/agent-service/src/agents/disk_inspection/agent/tools/DiskTools.py:DiskTools"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_020", "base_commit": "b59eeda56ee831f6aa18ec13fec3f685fd3e38fe", "patch": "", "problem_statement": "我想要把磁盘修复的结果上报给chatbot，要求在报告agent的处理过程中上报，无论失败还是成功都必须要上报。并且进行排版后上报", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py", "mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_019", "base_commit": "41e36354f97a509acbb1f6f747308edd4218a6fa", "patch": "", "problem_statement": "我想给磁盘巡检模块的上报流程中新增上报磁盘和设备的个数,要求在识别agent阶段上报给chatbot", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_algo.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_005", "base_commit": "e998ad2056a14428e46e4ef41346aca101cc09c7", "patch": "", "problem_statement": "我需要了解数据助手ps查询数据 发送到chatbot的逻辑，以便增加数据展示类型", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_prometheus/app_ps.py.query_prometheus", "mmvsops/mmvsops-agent/src/agents/agent_prometheus/utils/report.py:Report"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_029", "base_commit": "011daaf23c6744031c570d47c04da68bb2a5c788", "patch": "", "problem_statement": "帮我写一个用例用于验证一下这个磁盘巡检工具调用和上报的模块，要求验证到调用是否准确，以及上报是否符合预期", "edit_functions": ["mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_017", "base_commit": "f9297b147ed063aba4a2ec8835de6e58a56c88df", "patch": "", "problem_statement": "给磁盘巡检工具的上报流程增加返回值校验，如果上报失败，重复上报，最多三次，还是失败，日志记录失败报错 report.py（补充）", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py", "mmvsops-agent/src/agents/agent_disk/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_014", "base_commit": "e5f2ba86117849dd8cf398cb46325a364e282bd3", "patch": "", "problem_statement": "给日志记录功能增加回滚功能 1.要求每天的早上七点会重新生成一个同名日志文件，并且给昨天的按照后缀123的方式排序，直到排到9，把1删除，把2改成1，依此类推", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/common/log.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_016", "base_commit": "f9297b147ed063aba4a2ec8835de6e58a56c88df", "patch": "", "problem_statement": "我需要给函数disk_blockage_tool增加一个返回值，如果执行成功就返回true。失败就返回false", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py.disk_blockage_tool"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_037", "base_commit": "e998ad2056a14428e46e4ef41346aca101cc09c7", "patch": "", "problem_statement": "File \"/home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py\", line 4, in <module>      from crewai import Agent, Task    File \"/home/<USER>/.local/lib/python3.11/site-packages/crewai/\\_\\_init\\_\\_.py\", line 3, in <module>      from crewai.agent import Agent    File \"/home/<USER>/.local/lib/python3.11/site-packages/crewai/agent.py\", line 10, in <module>      from crewai.agents.crew\\_agent\\_executor import CrewAgentExecutor    File \"/home/<USER>/.local/lib/python3.11/site-packages/crewai/agents/crew\\_agent\\_executor.py\", line 16, in <module>      from crewai.llm import LLM    File \"/home/<USER>/.local/lib/python3.11/site-packages/crewai/llm.py\", line 15, in <module>      import litellm    File \"/home/<USER>", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_010", "base_commit": "48650bb721b35db0e24ec77376ada303f91a13b7", "patch": "", "problem_statement": "我想知道整个知识问答的全流程，想要给知识问答新增异常退出保护", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_kbqa/agent_kbqa.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_026", "base_commit": "51793a34bcd6a07f6b79dac770f63e38423a9786", "patch": "", "problem_statement": "我想写一个测试用例来测试获取salt_minion执行结果是否符合预期", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_013", "base_commit": "f9297b147ed063aba4a2ec8835de6e58a56c88df", "patch": "", "problem_statement": "我想要修改data数据里面第一次上报的内容，只上报磁盘个数和数量", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py.update_event"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_025", "base_commit": "e998ad2056a14428e46e4ef41346aca101cc09c7", "patch": "", "problem_statement": "帮我写一个测试用例验证一下获取salt_tool.py中query_task函数执行结果的输出", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py.query_task"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_006", "base_commit": "ff991f6ae85979a2d3077c3ff703d42d247e6412", "patch": "", "problem_statement": "我需要chatbot时间创建及上报全过程", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/report.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py.create_update_event"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_021", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "在用户输入查询当前天气的时候，拒绝调用，并且给用户返回可以查询的条目", "edit_functions": ["mmvsops/mmvsops-agent/src/app/intent_assistant/agent/intent.py", "mmvsops/mmvsops-agent/src/app/common/utils/config_loader.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_035", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "帮我写一个系统性的测试用例，测试内容主要是，从用户输入到磁盘巡检开始，直到得到执行结果上报之后的整个流程。", "edit_functions": ["mmvsops-agent/src/app/intent_assistant/agent/intent.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/main.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/disk_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_036", "base_commit": "b59eeda56ee831f6aa18ec13fec3f685fd3e38fe", "patch": "", "problem_statement": "帮我生成个系统性的测试用例，要求包括一下内容：  磁盘修复从节点触发之后，整个修复过程，知道修复完成或者失败上报到chatbot的整个流程。", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/main.py", "mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py", "mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_008", "base_commit": "07af3759c9f8b8d25745ef98f0f7d4b2a246e65c", "patch": "", "problem_statement": "帮我找到创建并初始化pg数据库表的调用和函数位置，我想修改出数据库的创建位置和表字段", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/psql_tool.py.init_db", "mmvsops/mmvsops-agent/src/agents/agent_disk/daemon.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_027", "base_commit": "07af3759c9f8b8d25745ef98f0f7d4b2a246e65c", "patch": "", "problem_statement": "帮我写一个测试用例测试一个 格式化为 ISO 8601 格式函数是否可用", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_algo.py.get_current_and_one_hour_ago"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_028", "base_commit": "07af3759c9f8b8d25745ef98f0f7d4b2a246e65c", "patch": "", "problem_statement": "帮我写一个测试用例，用于验证获取salt_token的函数是否可用", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py.get_salt_token"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_030", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "帮我写个用例用于验证磁盘巡检上报模块是否能成功上报，及上报结果是否符合预期", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py:DiskTools", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_003", "base_commit": "b59eeda56ee831f6aa18ec13fec3f685fd3e38fe", "patch": "", "problem_statement": "我想理解这段获取salt执行结果的函数，并且想加一个返回值，用来判断执行结果", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py.get_minion_result"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_007", "base_commit": "011daaf23c6744031c570d47c04da68bb2a5c788", "patch": "", "problem_statement": "我想理解磁盘定时巡检和立即巡检的区别以及对应的调用逻辑", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/daemon.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_024", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "新增一个接口，可以查询最近的磁盘修复记录", "edit_functions": ["mmvsops/mmvsops-agent/src/app/intent_assistant/agent/intent.py", "mmvsops/mmvsops-agent/src/agents/agent_diskrepair/main.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_015", "base_commit": "07af3759c9f8b8d25745ef98f0f7d4b2a246e65c", "patch": "", "problem_statement": "我想给数据库初始化函数新增一个字段用于记录这行数据的修改时间。", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/psql_tool.py.init_db"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_012", "base_commit": "4eb596f2fe9695d0116576fd4769995eac3b87cd", "patch": "", "problem_statement": "我想知道项目中普罗米修斯的查询全流程的流程,想要在整个流程增加异常退出保护", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_prometheus/main.py", "mmvsops/mmvsops-agent/src/agents/agent_prometheus/app_ps.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_004", "base_commit": "1a071af1717f3edcd72182d110939d191f8731fb", "patch": "", "problem_statement": "我需要理解磁盘工具支持的处理流程 以及各分支的作用 后续添加磁盘巡检新功能", "edit_functions": ["mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py:DiskTools"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_033", "base_commit": "4eb596f2fe9695d0116576fd4769995eac3b87cd", "patch": "", "problem_statement": "帮我写系统性测试用例，要求测试一下内容：  测试从用户输入设备ps指标查询开始到上报到chatbot的整个流程。", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_prometheus/main.py", "mmvsops/mmvsops-agent/src/agents/agent_prometheus/app_ps.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_022", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "对于磁盘巡检getCityDiskRisk函数都增加执行返回，记录在日志中", "edit_functions": ["mmvsops/mmvsops-agent/src/app/intent_assistant/agent/intent.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/main.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_009", "base_commit": "b59eeda56ee831f6aa18ec13fec3f685fd3e38fe", "patch": "", "problem_statement": "我需要知道磁盘修复直到上报的完整调用链", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_diskrepair/main.py", "mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/salt_tool.py", "mmvsops/mmvsops-agent/src/agents/agent_diskrepair/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_032", "base_commit": "48650bb721b35db0e24ec77376ada303f91a13b7", "patch": "", "problem_statement": "帮我写一个测试用例，用于验证返回给用户的文本是流式生成的", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_kbqa/agent_kbqa.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_011", "base_commit": "e8e6f91bb29f5b49eeb8a3dabd9644bcd6e6e5d1", "patch": "", "problem_statement": "我需要知道磁盘巡检的完整调用链,想要在整个流程增加异常退出保护", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/main.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/crew.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_018", "base_commit": "7da55b71a8758c38e60ad9473279da9c2df4363b", "patch": "", "problem_statement": "帮我在ps查询模块新增一查询失败的报错，并上报给chatbot，要求无论是查询失败或者是超时等情况，都需要结果", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_prometheus/app_ps.py", "mmvsops/mmvsops-agent/src/agents/agent_prometheus/utils/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_002", "base_commit": "1a071af1717f3edcd72182d110939d191f8731fb", "patch": "", "problem_statement": "我需要理解 crewai 流式响应如何实现的", "edit_functions": ["mmvsops-agent/src/agents/agent_disk/tools/stream_llm.py:StreamLLM"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_039", "base_commit": "79c7a6c1105fdb8b7af63d7f0e5e0701781b1f28", "patch": "", "problem_statement": "ValueError: Unknown scheme for proxy URL URL('socks://proxy.zte.com.cn:80')  [<EMAIL>@LIN-337B089D293 mmvsops]$ /bin/python /home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent\\_disk/tools/disk\\_tool.py    File \"/home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent\\_disk/tools/disk\\_tool.py\", line 146      Report.update(data                   ^  SyntaxError: '(' was never closed  [<EMAIL>@LIN-337B089D293 mmvsops]$ /bin/python /home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent\\_disk/tools/disk\\_tool.py    File \"/home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent\\_disk/tools/disk\\_tool.py\", line 146      Report.update(data                   ^  SyntaxError: '(' was never closed  [<EMAIL>@LIN-337B089D293 mmvsops]$ /bin/python /home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent\\_disk/tools/disk\\_tool.py    File \"/home/<USER>/Desktop/code/aiops/master/mmvsops/mmvsops-agent/src/agents/agent\\_disk/tools/disk\\_tool.py\", line 146      Report.update(data", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_038", "base_commit": "e998ad2056a14428e46e4ef41346aca101cc09c7", "patch": "", "problem_statement": "帮我删除没有使用但是导入的模块", "edit_functions": ["mmvsops/mmvsops-agent/src/agents/agent_disk/main.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_023", "base_commit": "011daaf23c6744031c570d47c04da68bb2a5c788", "patch": "", "problem_statement": "用户输入对于地区磁盘巡检的时候，将地区名称带给具体的处理agent，并且在上报事件的时候展示出来", "edit_functions": ["mmvsops/mmvsops-agent/src/app/intent_assistant/agent/intent.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/main.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/disk_tool.py", "mmvsops/mmvsops-agent/src/agents/agent_disk/tools/report.py"]}
{"repo": "ivamp/zxoms/mmvsops", "instance_id": "mmvsops_034", "base_commit": "1ce034834feb3ae68e9caa046249346fcf78d237", "patch": "", "problem_statement": "帮我写一个测试用例，要求测试内容是知识问答的从输入到流式返回给用户的整个流程", "edit_functions": ["mmvsops-agent/src/tool_agent_chatbot.py", "mmvsops/mmvsops-agent/src/agents/agent_chat/agent/agent_chatbot.py"]}
