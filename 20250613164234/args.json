{"localize": true, "merge": true, "use_example": false, "ranking_method": "mrr", "dataset": "/home/<USER>/Aini/testRepo/case/cdn/mmvsops_case.jsonl", "split": "test", "eval_n_limit": 1, "used_list": "selected_ids", "output_folder": "/home/<USER>/Aini/ZeroAgentCode/LocAgent/results/20250613164234", "output_file": "/home/<USER>/Aini/ZeroAgentCode/LocAgent/results/20250613164234/loc_outputs.jsonl", "merge_file": "merged_loc_outputs.jsonl", "model": "openai/Qwen3-235B-A22B", "use_function_calling": true, "simple_desc": true, "max_attempt_num": 1, "num_samples": 1, "num_processes": 1, "log_level": "INFO", "timeout": 900, "rerun_empty_location": false, "clone_dir": "/home/<USER>/Aini/testRepo"}